<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sccl.modules.business.job.mapper.ZndbDeviceAllMapper">

    <!-- 插入数据 -->
    <insert id="addZndbDeviceAll" parameterType="com.sccl.modules.business.job.domain.ZndbDeviceAll">
        INSERT INTO zndb_device_all (id,
                                     collectTime,
                                     proviceName,
                                     cityName,
                                     areaName,
                                     stationId,
                                     stationCode,
                                     stationName,
                                     stationLevel,
                                     buildingId,
                                     buildingCode,
                                     buildingName,
                                     buildingLevel,
                                     roomId,
                                     roomCode,
                                     roomName,
                                     roomLevel,
                                     meterId,
                                     meterCode,
                                     meterName,
                                     meterEnery,
                                     eneryType,
                                     loopId,
                                     createTime
        )
        VALUES (
                #{id},
                #{collectTime},
                #{proviceName},
                #{cityName},
                #{areaName},
                #{stationId},
                #{stationCode},
                #{stationName},
                #{stationLevel},
                #{buildingId},
                #{buildingCode},
                #{buildingName},
                #{buildingLevel},
                #{roomId},
                #{roomCode},
                #{roomName},
                #{roomLevel},
                #{meterId},
                #{meterCode},
                #{meterName},
                #{meterEnery},
                #{eneryType},
                #{loopId},
                #{createTime}
               )
    </insert>
    <select id="syncDate" parameterType="java.lang.String" resultType="com.sccl.modules.business.job.vo.CollectZndbData">
        SELECT DATE_FORMAT(zda.collectTime,'%Y%m%d') collectTime,
        substr(di.org_id,1,4) cityCode,
        zdi.org_name cityName,zdi1.org_id countyCode,zdi1.org_name countyName,
        roomId stationCode,
        roomName stationName,
        '' parentStationCode,
        '' ccoer,
        '' cdcf,
        round(IFNULL(SUM(case WHEN eneryType=1 THEN meterEnery END ),SUM(meterEnery)),2) energyData,
        '110'	energyDataSource,
        '0.00' acData,'223' acDataSource,'0.00' oepgData,'1' oepgDataSource,'0.00' pvpgData,'1' pvpgDataSource,
        IFNULL(round(SUM(case WHEN eneryType=3 THEN meterEnery END ),2),0)  deviceData,
        '1'	deviceDataSource,
        IFNULL(round(SUM(case WHEN eneryType=2 THEN meterEnery END ),2),0)  productionData,
        '1'	productionDataSource,
        IFNULL(round(SUM(case WHEN eneryType=4 THEN meterEnery END ),2),0) managementData,
        '1'	managementDataSource,
        IFNULL(round(SUM(case WHEN eneryType=5 THEN meterEnery END ),2),0) businessData,
        '1'	businessDataSource,
        IFNULL(round(SUM(case WHEN eneryType=6 THEN meterEnery END ),2),0) otherData,
        '1'	otherDataSource
        FROM zndb_device_all zda,zndb_department_id  zdi,zndb_department_id  zdi1,department_id di
        WHERE
            zda.collectTime=#{collectTime}
          AND  di.LEVEL_id=zdi.org_id
          AND di.LEVEL_id = zdi.org_id
          AND zda.meterEnery > 0
          AND zda.cityName = zdi.org_name
          AND zda.areaName = zdi1.org_name
          AND zda.roomId is not NULL
        GROUP BY roomId;
    </select>
</mapper>
