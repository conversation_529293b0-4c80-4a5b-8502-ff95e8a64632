{"groups": [{"name": "contract.ftp", "type": "com.sccl.modules.business.contractsync.config.FtpConfig", "sourceType": "com.sccl.modules.business.contractsync.config.FtpConfig"}, {"name": "redis.pool", "type": "com.sccl.modules.business.datafilter.config.JedisConfig", "sourceType": "com.sccl.modules.business.datafilter.config.JedisConfig"}], "properties": [{"name": "contract.ftp.connect-timeout", "type": "java.lang.Integer", "description": "连接超时时间（毫秒）", "sourceType": "com.sccl.modules.business.contractsync.config.FtpConfig", "defaultValue": 30000}, {"name": "contract.ftp.data-timeout", "type": "java.lang.Integer", "description": "数据传输超时时间（毫秒）", "sourceType": "com.sccl.modules.business.contractsync.config.FtpConfig", "defaultValue": 60000}, {"name": "contract.ftp.host", "type": "java.lang.String", "description": "FTP服务器地址", "sourceType": "com.sccl.modules.business.contractsync.config.FtpConfig", "defaultValue": "**************"}, {"name": "contract.ftp.local-dir", "type": "java.lang.String", "description": "本地下载目录", "sourceType": "com.sccl.modules.business.contractsync.config.FtpConfig", "defaultValue": "/apps/NhApp/ftp-contract"}, {"name": "contract.ftp.password", "type": "java.lang.String", "description": "FTP密码", "sourceType": "com.sccl.modules.business.contractsync.config.FtpConfig", "defaultValue": "Inf_01a^1"}, {"name": "contract.ftp.port", "type": "java.lang.Integer", "description": "FTP服务器端口", "sourceType": "com.sccl.modules.business.contractsync.config.FtpConfig", "defaultValue": 21}, {"name": "contract.ftp.remote-dir", "type": "java.lang.String", "description": "FTP远程目录", "sourceType": "com.sccl.modules.business.contractsync.config.FtpConfig", "defaultValue": "/edw/nenghao"}, {"name": "contract.ftp.username", "type": "java.lang.String", "description": "FTP用户名", "sourceType": "com.sccl.modules.business.contractsync.config.FtpConfig", "defaultValue": "edw"}, {"name": "redis.pool.block-when-exhausted", "type": "java.lang.Bo<PERSON>an", "sourceType": "com.sccl.modules.business.datafilter.config.JedisConfig", "defaultValue": false}, {"name": "redis.pool.database", "type": "java.lang.Integer", "sourceType": "com.sccl.modules.business.datafilter.config.JedisConfig", "defaultValue": 0}, {"name": "redis.pool.host", "type": "java.lang.String", "sourceType": "com.sccl.modules.business.datafilter.config.JedisConfig"}, {"name": "redis.pool.max-idle", "type": "java.lang.Integer", "sourceType": "com.sccl.modules.business.datafilter.config.JedisConfig", "defaultValue": 0}, {"name": "redis.pool.max-total", "type": "java.lang.Integer", "sourceType": "com.sccl.modules.business.datafilter.config.JedisConfig", "defaultValue": 0}, {"name": "redis.pool.max-wait-millis", "type": "java.lang.Integer", "sourceType": "com.sccl.modules.business.datafilter.config.JedisConfig", "defaultValue": 0}, {"name": "redis.pool.min-idle", "type": "java.lang.Integer", "sourceType": "com.sccl.modules.business.datafilter.config.JedisConfig", "defaultValue": 0}, {"name": "redis.pool.password", "type": "java.lang.String", "sourceType": "com.sccl.modules.business.datafilter.config.JedisConfig"}, {"name": "redis.pool.port", "type": "java.lang.Integer", "sourceType": "com.sccl.modules.business.datafilter.config.JedisConfig", "defaultValue": 0}, {"name": "redis.pool.timeout", "type": "java.lang.Integer", "sourceType": "com.sccl.modules.business.datafilter.config.JedisConfig", "defaultValue": 0}], "hints": []}