package com.sccl.modules.business.job.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * 智能电表设备
 */
@Getter
@Setter
public class ZndbDeviceAll {

    /**
     * id
     */
    private Long id;

    /**
     * 账期(天)
     */
    @JsonFormat(pattern = "yyyyMMdd")
    @NotNull(message = "账期不能为空")
    private Date collectTime;

    /**
     * 省名称
     */
    @NotBlank(message = "省名称不能为空")
    private String proviceName;

    /**
     * 市名称
     */
    @NotBlank(message = "市名称不能为空")
    private String cityName;

    /**
     * 区/县名称
     */
    @NotBlank(message = "区/县名称不能为空")
    private String areaName;

    /**
     * 局站id
     */
    @NotNull(message = "局站id不能为空")
    private String stationId;

    /**
     * 局站编码
     */
    @NotNull(message = "局站编码不能为空")
    private String stationCode;

    /**
     * 局站名称
     */
    @NotBlank(message = "局站名称不能为空")
    private String stationName;

    /**
     * 局站等级
     */
    @NotBlank(message = "局站等级不能为空")
    private String stationLevel;

    /**
     * 机楼id
     */
    private String buildingId;

    /**
     * 机楼编码
     */
    private String buildingCode;

    /**
     * 机楼名称
     */
    private String buildingName;

    /**
     * 机楼等级
     */
    private String buildingLevel;

    /**
     * 机房id
     */
    private String roomId;

    /**
     * 机房编码
     */
    private String roomCode;

    /**
     * 机房名称
     */
    private String roomName;

    /**
     * 机房等级
     */
    private String roomLevel;

    /**
     * 电表id
     */
    private String meterId;

    /**
     * 回路id
     */
    private Long loopId;

    /**
     * 电表编码
     */
    private String meterCode;

    /**
     * 电表名称
     */
    private String meterName;

    /**
     * 电表日能耗
     */
    @NotNull(message = "电表日能耗不能为空")
    private String meterEnery;

    /**
     * 用电类型
     */
    @NotBlank(message = "用电类型不能为空")
    private String eneryType;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
}
