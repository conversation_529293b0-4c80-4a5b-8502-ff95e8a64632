<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="3f48106f-a6ca-4aca-b8f1-40d649d6fd18" name="更改" comment="perf(oss): 优化 MinIO 预览适配&#10;&#10;- 修改 Nginx 配置，设置正确的 Host 头以支持 MinIO 请求&#10;- 传递原始 Host 给后端，用于日志记录&#10;- 移除后端预览 URL 获取接口，直接在前端构建预览 URL&#10;-优化图片加载逻辑，提高预览速度">
      <change beforePath="$PROJECT_DIR$/vue.config.js" beforeDir="false" afterPath="$PROJECT_DIR$/vue.config.js" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_BRANCH_BY_REPOSITORY">
      <map>
        <entry key="$PROJECT_DIR$" value="dev" />
      </map>
    </option>
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="customMavenHome" value="D:\soft\apache-maven-3.6.0" />
        <option name="localRepository" value="E:\.m2\repository" />
        <option name="mavenHomeTypeForPersistence" value="CUSTOM" />
        <option name="userSettingsFile" value="E:\.m2\settings-cl.xml" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 5
}</component>
  <component name="ProjectId" id="2tyHdoAObveBruk9BP7vqu8mOE1" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;git-widget-placeholder&quot;: &quot;main__20250710&quot;,
    &quot;kotlin-language-version-configured&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;E:/cl-project/ln-nenghao/brch-ln-vue&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.standard&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.standard&quot;: &quot;&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;npm.build-ln.executor&quot;: &quot;Run&quot;,
    &quot;npm.dev-ln.executor&quot;: &quot;Run&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;preferences.sourceCode.Vue 模板&quot;,
    &quot;ts.external.directory.path&quot;: &quot;D:\\soft\\JetBrains\\IntelliJ IDEA 2024.3.3\\plugins\\javascript-plugin\\jsLanguageServicesImpl\\external&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="RebelAgentSelection">
    <selection>jr</selection>
  </component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="E:\cl-project\brch-ln-vue" />
      <recent name="E:\cl-project\brch-ln-vue\docker\conf.d" />
      <recent name="E:\cl-project\brch-ln-vue\src" />
      <recent name="E:\cl-project\brch-ln-vue\src\store\module" />
      <recent name="E:\cl-project\brch-ln-vue\src\directive" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="E:\cl-project\brch-ln-vue\docker\conf.d" />
    </key>
  </component>
  <component name="RunManager" selected="npm.dev-ln">
    <configuration name="build-ln" type="js.build_tools.npm" temporary="true" nameIsGenerated="true">
      <package-json value="$PROJECT_DIR$/package.json" />
      <command value="run" />
      <scripts>
        <script value="build-ln" />
      </scripts>
      <node-interpreter value="D:/soft/nvm/v14.21.3/node" />
      <envs />
      <method v="2" />
    </configuration>
    <configuration name="dev-ln" type="js.build_tools.npm" temporary="true" nameIsGenerated="true">
      <package-json value="$PROJECT_DIR$/package.json" />
      <command value="run" />
      <scripts>
        <script value="dev-ln" />
      </scripts>
      <node-interpreter value="D:/soft/nvm/v14.21.3/node" />
      <envs />
      <method v="2" />
    </configuration>
    <list>
      <item itemvalue="npm.build-ln" />
      <item itemvalue="npm.dev-ln" />
    </list>
    <recent_temporary>
      <list>
        <item itemvalue="npm.dev-ln" />
        <item itemvalue="npm.build-ln" />
        <item itemvalue="npm.dev-ln" />
        <item itemvalue="npm.build-ln" />
        <item itemvalue="npm.dev-ln" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-fbdcb00ec9e3-intellij.indexing.shared.core-IU-251.27812.49" />
        <option value="bundled-js-predefined-d6986cc7102b-09060db00ec0-JavaScript-IU-251.27812.49" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="StructureViewState">
    <option name="selectedTab" value="逻辑" />
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="3f48106f-a6ca-4aca-b8f1-40d649d6fd18" name="更改" comment="" />
      <created>1741317483617</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1741317483617</updated>
      <workItem from="1741317484718" duration="3979000" />
      <workItem from="1741334952810" duration="1462000" />
      <workItem from="1741589245899" duration="2062000" />
      <workItem from="1741656472344" duration="7715000" />
      <workItem from="1741742453920" duration="6000" />
      <workItem from="1741764991935" duration="6282000" />
      <workItem from="1741836646598" duration="60000" />
      <workItem from="1741851423380" duration="981000" />
      <workItem from="1741852430415" duration="2041000" />
      <workItem from="1741916250661" duration="17000" />
      <workItem from="1742182914839" duration="4746000" />
      <workItem from="1742204334399" duration="643000" />
      <workItem from="1742264531205" duration="7000" />
      <workItem from="1742377575464" duration="9552000" />
      <workItem from="1742433952199" duration="9236000" />
      <workItem from="1742520831609" duration="23000" />
      <workItem from="1742520860649" duration="5161000" />
      <workItem from="1742539721821" duration="5150000" />
      <workItem from="1742559912377" duration="1403000" />
      <workItem from="1742780176718" duration="2191000" />
      <workItem from="1742864227536" duration="597000" />
      <workItem from="1742957375930" duration="15000" />
      <workItem from="1742957400945" duration="4935000" />
      <workItem from="1743041010146" duration="1940000" />
      <workItem from="1743126035227" duration="3683000" />
      <workItem from="1743166906392" duration="11000" />
      <workItem from="1743387247369" duration="1272000" />
      <workItem from="1743401350708" duration="230000" />
      <workItem from="1743402248049" duration="1381000" />
      <workItem from="1743470586584" duration="2000" />
      <workItem from="1743471532128" duration="2659000" />
      <workItem from="1743560895312" duration="67000" />
      <workItem from="1743571608240" duration="2038000" />
      <workItem from="1743666247448" duration="637000" />
      <workItem from="1743988178046" duration="102000" />
      <workItem from="1743994531563" duration="3666000" />
      <workItem from="1744075304458" duration="1598000" />
      <workItem from="1744084089955" duration="12308000" />
      <workItem from="1744164852053" duration="5949000" />
      <workItem from="1744196491298" duration="4855000" />
      <workItem from="1744204505274" duration="1624000" />
      <workItem from="1744248321731" duration="5693000" />
      <workItem from="1744333492912" duration="61000" />
      <workItem from="1744884831776" duration="29000" />
      <workItem from="1744946753760" duration="15068000" />
      <workItem from="1745198750712" duration="13598000" />
      <workItem from="1745375895449" duration="10110000" />
      <workItem from="1745466982003" duration="13304000" />
      <workItem from="1745548122750" duration="3796000" />
      <workItem from="1745716373206" duration="8000" />
      <workItem from="1745734392523" duration="9933000" />
      <workItem from="1745907690105" duration="2687000" />
      <workItem from="1746501702493" duration="6651000" />
      <workItem from="1746582844758" duration="3519000" />
      <workItem from="1746670305575" duration="1141000" />
      <workItem from="1746761018446" duration="4719000" />
      <workItem from="1747017476993" duration="830000" />
      <workItem from="1747019209200" duration="3472000" />
      <workItem from="1747097895705" duration="296000" />
      <workItem from="1747098200659" duration="603000" />
      <workItem from="1747099319548" duration="4954000" />
      <workItem from="1747185948569" duration="8000" />
      <workItem from="1747185962764" duration="8635000" />
      <workItem from="1747299674060" duration="382000" />
      <workItem from="1747385485124" duration="698000" />
      <workItem from="1747638679040" duration="4008000" />
      <workItem from="1747706681984" duration="186000" />
      <workItem from="1747706879058" duration="12926000" />
      <workItem from="1747789772323" duration="15418000" />
      <workItem from="1747876422003" duration="13000" />
      <workItem from="1747876441152" duration="5212000" />
      <workItem from="1747885898349" duration="3263000" />
      <workItem from="1747962959069" duration="3700000" />
      <workItem from="1748221370056" duration="650000" />
      <workItem from="1748222033942" duration="13000" />
      <workItem from="1748222053896" duration="6924000" />
      <workItem from="1748308469978" duration="337000" />
      <workItem from="1748308823934" duration="4018000" />
      <workItem from="1748330656192" duration="1710000" />
      <workItem from="1748336352601" duration="551000" />
      <workItem from="1748394925996" duration="8000" />
      <workItem from="1748394969239" duration="2231000" />
      <workItem from="1748521801337" duration="1544000" />
      <workItem from="1748919585927" duration="3292000" />
      <workItem from="1748998918658" duration="4000" />
      <workItem from="1749023338001" duration="628000" />
      <workItem from="1749087917794" duration="2581000" />
      <workItem from="1749105282609" duration="1512000" />
      <workItem from="1749175268649" duration="6029000" />
      <workItem from="1749431385692" duration="6000" />
      <workItem from="1749431465641" duration="4028000" />
      <workItem from="1749537662213" duration="597000" />
      <workItem from="1749727936328" duration="121000" />
      <workItem from="1749781499278" duration="2646000" />
      <workItem from="1750037167042" duration="8000" />
      <workItem from="1750057260239" duration="624000" />
      <workItem from="1750321476924" duration="3045000" />
      <workItem from="1750398106104" duration="1429000" />
      <workItem from="1750402984773" duration="1638000" />
      <workItem from="1750729164576" duration="2095000" />
      <workItem from="1750747634046" duration="5249000" />
      <workItem from="1750821664295" duration="3067000" />
      <workItem from="1751012975217" duration="6764000" />
      <workItem from="1751246011694" duration="13000" />
      <workItem from="1751246036492" duration="13797000" />
      <workItem from="1751335966493" duration="3512000" />
      <workItem from="1751346715035" duration="2066000" />
      <workItem from="1751450303940" duration="731000" />
      <workItem from="1751505803621" duration="15000" />
      <workItem from="1751521111769" duration="2398000" />
      <workItem from="1751594245167" duration="793000" />
      <workItem from="1751850037684" duration="614000" />
      <workItem from="1752045194296" duration="1887000" />
      <workItem from="1752109224362" duration="614000" />
      <workItem from="1752109920487" duration="14482000" />
      <workItem from="1752139203409" duration="112000" />
      <workItem from="1752198086426" duration="124000" />
      <workItem from="1752198218208" duration="7338000" />
      <workItem from="1752231433007" duration="1990000" />
      <workItem from="1752460846648" duration="9452000" />
      <workItem from="1752543816694" duration="3540000" />
      <workItem from="1752560411077" duration="2242000" />
      <workItem from="1752628806298" duration="2411000" />
      <workItem from="1752717854439" duration="1961000" />
      <workItem from="1752806694442" duration="1270000" />
      <workItem from="1752822578597" duration="2067000" />
      <workItem from="1753059478508" duration="10000" />
      <workItem from="1753059496544" duration="4711000" />
      <workItem from="1753249496249" duration="11000" />
      <workItem from="1753321704123" duration="2876000" />
      <workItem from="1753668906267" duration="5250000" />
      <workItem from="1753757371745" duration="4500000" />
      <workItem from="1753839446660" duration="3000" />
      <workItem from="1753839463148" duration="7895000" />
      <workItem from="1753872657087" duration="815000" />
      <workItem from="1753874991442" duration="5000" />
      <workItem from="1753875001745" duration="2081000" />
      <workItem from="1753877336348" duration="192000" />
      <workItem from="1753929298870" duration="2199000" />
      <workItem from="1754010451222" duration="5566000" />
      <workItem from="1754276118493" duration="4462000" />
      <workItem from="1754379571365" duration="303000" />
    </task>
    <task id="LOCAL-00005" summary="nginx配置">
      <option name="closed" value="true" />
      <created>1741765116626</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>1741765116626</updated>
    </task>
    <task id="LOCAL-00006" summary="nginx配置">
      <option name="closed" value="true" />
      <created>1741855935598</created>
      <option name="number" value="00006" />
      <option name="presentableId" value="LOCAL-00006" />
      <option name="project" value="LOCAL" />
      <updated>1741855935598</updated>
    </task>
    <task id="LOCAL-00007" summary="完善：资源局站id显示与查询">
      <option name="closed" value="true" />
      <created>1742392418730</created>
      <option name="number" value="00007" />
      <option name="presentableId" value="LOCAL-00007" />
      <option name="project" value="LOCAL" />
      <updated>1742392418730</updated>
    </task>
    <task id="LOCAL-00008" summary="完善：资源局站id显示与查询">
      <option name="closed" value="true" />
      <created>1742539790230</created>
      <option name="number" value="00008" />
      <option name="presentableId" value="LOCAL-00008" />
      <option name="project" value="LOCAL" />
      <updated>1742539790230</updated>
    </task>
    <task id="LOCAL-00009" summary="完善：资源局站id显示与查询；删除alert(1)提示">
      <option name="closed" value="true" />
      <created>1742541670021</created>
      <option name="number" value="00009" />
      <option name="presentableId" value="LOCAL-00009" />
      <option name="project" value="LOCAL" />
      <updated>1742541670021</updated>
    </task>
    <task id="LOCAL-00010" summary="完善：资源局站id显示与查询；删除alert(1)提示">
      <option name="closed" value="true" />
      <created>1742541868824</created>
      <option name="number" value="00010" />
      <option name="presentableId" value="LOCAL-00010" />
      <option name="project" value="LOCAL" />
      <updated>1742541868824</updated>
    </task>
    <task id="LOCAL-00011" summary="完善：资源局站id显示与查询；优化提示">
      <option name="closed" value="true" />
      <created>1742542115228</created>
      <option name="number" value="00011" />
      <option name="presentableId" value="LOCAL-00011" />
      <option name="project" value="LOCAL" />
      <updated>1742542115228</updated>
    </task>
    <task id="LOCAL-00012" summary="完善：资源局站id显示与查询；重置查询功能优化">
      <option name="closed" value="true" />
      <created>1742545379962</created>
      <option name="number" value="00012" />
      <option name="presentableId" value="LOCAL-00012" />
      <option name="project" value="LOCAL" />
      <updated>1742545379962</updated>
    </task>
    <task id="LOCAL-00013" summary="修复：局站的资源局站id修改">
      <option name="closed" value="true" />
      <created>1744107882190</created>
      <option name="number" value="00013" />
      <option name="presentableId" value="LOCAL-00013" />
      <option name="project" value="LOCAL" />
      <updated>1744107882190</updated>
    </task>
    <task id="LOCAL-00014" summary="修复：局站的资源局站id修改">
      <option name="closed" value="true" />
      <created>1744181805000</created>
      <option name="number" value="00014" />
      <option name="presentableId" value="LOCAL-00014" />
      <option name="project" value="LOCAL" />
      <updated>1744181805000</updated>
    </task>
    <task id="LOCAL-00015" summary="修复：局站的资源局站id修改-去掉alert">
      <option name="closed" value="true" />
      <created>1744188475112</created>
      <option name="number" value="00015" />
      <option name="presentableId" value="LOCAL-00015" />
      <option name="project" value="LOCAL" />
      <updated>1744188475112</updated>
    </task>
    <task id="LOCAL-00016" summary="修复：电表-局站的资源局站id修改">
      <option name="closed" value="true" />
      <created>1744200525375</created>
      <option name="number" value="00016" />
      <option name="presentableId" value="LOCAL-00016" />
      <option name="project" value="LOCAL" />
      <updated>1744200525375</updated>
    </task>
    <task id="LOCAL-00017" summary="修复：电表-局站的资源局站id修改">
      <option name="closed" value="true" />
      <created>1744272848454</created>
      <option name="number" value="00017" />
      <option name="presentableId" value="LOCAL-00017" />
      <option name="project" value="LOCAL" />
      <updated>1744272848454</updated>
    </task>
    <task id="LOCAL-00018" summary="修改nginx配置">
      <option name="closed" value="true" />
      <created>1745224422998</created>
      <option name="number" value="00018" />
      <option name="presentableId" value="LOCAL-00018" />
      <option name="project" value="LOCAL" />
      <updated>1745224422998</updated>
    </task>
    <task id="LOCAL-00019" summary="修改nginx配置">
      <option name="closed" value="true" />
      <created>1745224946910</created>
      <option name="number" value="00019" />
      <option name="presentableId" value="LOCAL-00019" />
      <option name="project" value="LOCAL" />
      <updated>1745224946910</updated>
    </task>
    <task id="LOCAL-00020" summary="取消提示：只能手工录入新建、换表铁塔台账">
      <option name="closed" value="true" />
      <created>1745409148026</created>
      <option name="number" value="00020" />
      <option name="presentableId" value="LOCAL-00020" />
      <option name="project" value="LOCAL" />
      <updated>1745409148026</updated>
    </task>
    <task id="LOCAL-00021" summary="修改nginx配置">
      <option name="closed" value="true" />
      <created>1745423549972</created>
      <option name="number" value="00021" />
      <option name="presentableId" value="LOCAL-00021" />
      <option name="project" value="LOCAL" />
      <updated>1745423549972</updated>
    </task>
    <task id="LOCAL-00022" summary="refactor(docker): 重构 Docker 部署配置&#10;&#10;-移除原有的 docker-compose.yaml 文件- 将 Dockerfile 从 docker 目录移动到 docker-prod 目录&#10;- 新增 docker-test 目录，用于测试环境的 Docker 部署&#10;- 新增测试环境的 nginx 配置文件&#10;- 更新 Dockerfile，使用新的测试环境配置">
      <option name="closed" value="true" />
      <created>1745738913653</created>
      <option name="number" value="00022" />
      <option name="presentableId" value="LOCAL-00022" />
      <option name="project" value="LOCAL" />
      <updated>1745738913653</updated>
    </task>
    <task id="LOCAL-00023" summary="build(docker): 更新 Dockerfile配置&#10;&#10;- 修改 docker-prod/Dockerfile，将 test.conf替换为 prod.conf&#10;- 修改 docker-test/Dockerfile，将 prod.conf 替换为 test.conf">
      <option name="closed" value="true" />
      <created>1745740434522</created>
      <option name="number" value="00023" />
      <option name="presentableId" value="LOCAL-00023" />
      <option name="project" value="LOCAL" />
      <updated>1745740434522</updated>
    </task>
    <task id="LOCAL-00024" summary="build(docker): 更新 Dockerfile 文件路径&#10;&#10;- 修改 docker-prod/Dockerfile 中的配置文件路径&#10;- 修改 docker-test/Dockerfile 中的配置文件路径">
      <option name="closed" value="true" />
      <created>1745741738735</created>
      <option name="number" value="00024" />
      <option name="presentableId" value="LOCAL-00024" />
      <option name="project" value="LOCAL" />
      <updated>1745741738735</updated>
    </task>
    <task id="LOCAL-00025" summary="fix(docker): 修正 Dockerfile 中的文件路径&#10;&#10;- 将 'docker-pord' 修正为 'docker-prod'，修复了文件路径的拼写错误&#10;- 更新了 prod.conf 和 nginx.conf 文件的正确路径">
      <option name="closed" value="true" />
      <created>1745742625525</created>
      <option name="number" value="00025" />
      <option name="presentableId" value="LOCAL-00025" />
      <option name="project" value="LOCAL" />
      <updated>1745742625525</updated>
    </task>
    <task id="LOCAL-00026" summary="build(docker): 更新 Nginx 配置文件路径&#10;&#10;-将 prod.conf 和 test.conf 文件的路径修改为 default.conf&#10;-这个改动统一了 Nginx 配置文件的路径，简化了部署流程">
      <option name="closed" value="true" />
      <created>1745743874294</created>
      <option name="number" value="00026" />
      <option name="presentableId" value="LOCAL-00026" />
      <option name="project" value="LOCAL" />
      <updated>1745743874294</updated>
    </task>
    <task id="LOCAL-00027" summary="refactor: 修改监听端口为 80&#10;&#10;- 将 server 配置中的 listen 指令从 8088改为 80&#10;-此修改使得服务可以通过标准的 HTTP 端口进行访问">
      <option name="closed" value="true" />
      <created>1745747384846</created>
      <option name="number" value="00027" />
      <option name="presentableId" value="LOCAL-00027" />
      <option name="project" value="LOCAL" />
      <updated>1745747384846</updated>
    </task>
    <task id="LOCAL-00028" summary="fix(carbon): 修复绿色能源管理保存逻辑&#10;&#10;- 在保存成功后添加对返回码的判断- 只有当返回码为">
      <option name="closed" value="true" />
      <created>1747809466739</created>
      <option name="number" value="00028" />
      <option name="presentableId" value="LOCAL-00028" />
      <option name="project" value="LOCAL" />
      <updated>1747809466739</updated>
    </task>
    <task id="LOCAL-00029" summary="refactor(view): 优化首页数据展示&#10;&#10;- 移除用电量和生产用电数据中的toFixed(2)调用&#10;- 保留原始数据精度，避免不必要的小数位数">
      <option name="closed" value="true" />
      <created>1747815276044</created>
      <option name="number" value="00029" />
      <option name="presentableId" value="LOCAL-00029" />
      <option name="project" value="LOCAL" />
      <updated>1747815276044</updated>
    </task>
    <task id="LOCAL-00030" summary="refactor(view): 修改考核部门列的显示内容&#10;&#10;- 将考核部门列的 prop 从 &quot;objectType&quot; 修改为 &quot;companyName&quot;- 移除了不必要的 slot 和 formatter 函数">
      <option name="closed" value="true" />
      <created>1748334260063</created>
      <option name="number" value="00030" />
      <option name="presentableId" value="LOCAL-00030" />
      <option name="project" value="LOCAL" />
      <updated>1748334260063</updated>
    </task>
    <task id="LOCAL-00031" summary="build(deps): 优化 vue-loader-v16 依赖配置&#10;&#10;- 移除重复的 big.js、emojis-list、has-flag等依赖项&#10;- 调整 loader-utils 依赖版本&#10;- 删除不必要的嵌套依赖结构">
      <option name="closed" value="true" />
      <created>1749432024259</created>
      <option name="number" value="00031" />
      <option name="presentableId" value="LOCAL-00031" />
      <option name="project" value="LOCAL" />
      <updated>1749432024259</updated>
    </task>
    <task id="LOCAL-00032" summary="feat(proxy): 添加能源接口服务配置并更新开发环境配置&#10;&#10;- 在生产环境和测试环境的 Nginx 配置中添加了 /energy-interface/ 服务的代理配置&#10;- 更新了 Vue 开发环境中的代理目标地址">
      <option name="closed" value="true" />
      <created>1750323046630</created>
      <option name="number" value="00032" />
      <option name="presentableId" value="LOCAL-00032" />
      <option name="project" value="LOCAL" />
      <updated>1750323046630</updated>
    </task>
    <task id="LOCAL-00033" summary="refactor(view): 优化 countryModal 组件代码结构&#10;&#10;-调整代码缩进和格式，提高可读性">
      <option name="closed" value="true" />
      <created>1750746949880</created>
      <option name="number" value="00033" />
      <option name="presentableId" value="LOCAL-00033" />
      <option name="project" value="LOCAL" />
      <updated>1750746949880</updated>
    </task>
    <task id="LOCAL-00034" summary="feat(station): 局站编辑框支持机房选择功能">
      <option name="closed" value="true" />
      <created>1751015513361</created>
      <option name="number" value="00034" />
      <option name="presentableId" value="LOCAL-00034" />
      <option name="project" value="LOCAL" />
      <updated>1751015513361</updated>
    </task>
    <task id="LOCAL-00035" summary="feat(basedata): 添加在网表计清单功能">
      <option name="closed" value="true" />
      <created>1751261603901</created>
      <option name="number" value="00035" />
      <option name="presentableId" value="LOCAL-00035" />
      <option name="project" value="LOCAL" />
      <updated>1751261603901</updated>
    </task>
    <task id="LOCAL-00036" summary="增加判断 基站及D类放行">
      <option name="closed" value="true" />
      <created>1752046691762</created>
      <option name="number" value="00036" />
      <option name="presentableId" value="LOCAL-00036" />
      <option name="project" value="LOCAL" />
      <updated>1752046691762</updated>
    </task>
    <task id="LOCAL-00037" summary="增加判断 基站及D类放行时移除重复的错误提示逻辑">
      <option name="closed" value="true" />
      <created>1752116691946</created>
      <option name="number" value="00037" />
      <option name="presentableId" value="LOCAL-00037" />
      <option name="project" value="LOCAL" />
      <updated>1752116691946</updated>
    </task>
    <task id="LOCAL-00038" summary="添加表计清单查询功能">
      <option name="closed" value="true" />
      <created>1752139299815</created>
      <option name="number" value="00038" />
      <option name="presentableId" value="LOCAL-00038" />
      <option name="project" value="LOCAL" />
      <updated>1752139299815</updated>
    </task>
    <task id="LOCAL-00039" summary="refactor(view): 移除 meterinfoAllJt 组件中的加载指示器">
      <option name="closed" value="true" />
      <created>1752198365202</created>
      <option name="number" value="00039" />
      <option name="presentableId" value="LOCAL-00039" />
      <option name="project" value="LOCAL" />
      <updated>1752198365202</updated>
    </task>
    <task id="LOCAL-00040" summary="refactor(view): 添加表计清单导入功能">
      <option name="closed" value="true" />
      <created>1752212488662</created>
      <option name="number" value="00040" />
      <option name="presentableId" value="LOCAL-00040" />
      <option name="project" value="LOCAL" />
      <updated>1752212488662</updated>
    </task>
    <task id="LOCAL-00041" summary="feat(basedata): 添加表计清单查询相关权限控制">
      <option name="closed" value="true" />
      <created>1752213944445</created>
      <option name="number" value="00041" />
      <option name="presentableId" value="LOCAL-00041" />
      <option name="project" value="LOCAL" />
      <updated>1752213944445</updated>
    </task>
    <task id="LOCAL-00042" summary="refactor(router): 移除在网表计数据清单路由">
      <option name="closed" value="true" />
      <created>1752473060069</created>
      <option name="number" value="00042" />
      <option name="presentableId" value="LOCAL-00042" />
      <option name="project" value="LOCAL" />
      <updated>1752473060069</updated>
    </task>
    <task id="LOCAL-00043" summary="feat(docker): 添加 library-api接口代理配置">
      <option name="closed" value="true" />
      <created>1752494386760</created>
      <option name="number" value="00043" />
      <option name="presentableId" value="LOCAL-00043" />
      <option name="project" value="LOCAL" />
      <updated>1752494386760</updated>
    </task>
    <task id="LOCAL-00044" summary="update">
      <option name="closed" value="true" />
      <created>1753336233228</created>
      <option name="number" value="00044" />
      <option name="presentableId" value="LOCAL-00044" />
      <option name="project" value="LOCAL" />
      <updated>1753336233228</updated>
    </task>
    <task id="LOCAL-00045" summary="update package-lock.json">
      <option name="closed" value="true" />
      <created>1753773839582</created>
      <option name="number" value="00045" />
      <option name="presentableId" value="LOCAL-00045" />
      <option name="project" value="LOCAL" />
      <updated>1753773839582</updated>
    </task>
    <task id="LOCAL-00046" summary="feat(docker-prod): 添加对象存储服务访问配置&#10;&#10;- 在 prod.conf 文件中添加了 /ln-nh-oss/ 位置的代理配置&#10;- 通过此配置，可以访问对象存储服务">
      <option name="closed" value="true" />
      <created>*************</created>
      <option name="number" value="00046" />
      <option name="presentableId" value="LOCAL-00046" />
      <option name="project" value="LOCAL" />
      <updated>*************</updated>
    </task>
    <task id="LOCAL-00047" summary="fix(account): 修复在线预览文件路径问题&#10;&#10;- 在 uploadFileModal.vue 文件中，为适应 minio 迁移，增加了获取当前域名作为 baseUrl 的逻辑&#10;-将 row.url 前面加上 baseUrl，确保生成的预览 URL 是完整的&#10;- 这个修改同时应用于 oilAccount 和 account 模块中的 uploadFileModal 组件">
      <option name="closed" value="true" />
      <created>*************</created>
      <option name="number" value="00047" />
      <option name="presentableId" value="LOCAL-00047" />
      <option name="project" value="LOCAL" />
      <updated>*************</updated>
    </task>
    <task id="LOCAL-00048" summary="refactor(docker-prod):调整 Nginx 配置&#10;&#10;- 移动对象存储服务配置到静态资源配置之前- 优化静态资源配置，使其更容易理解和维护&#10;- 删除重复的对象存储服务配置">
      <option name="closed" value="true" />
      <created>*************</created>
      <option name="number" value="00048" />
      <option name="presentableId" value="LOCAL-00048" />
      <option name="project" value="LOCAL" />
      <updated>*************</updated>
    </task>
    <task id="LOCAL-00049" summary="perf(docker-prod): 优化对象存储服务访问配置&#10;&#10;- 修改 Host 头设置，使用 $http_host 替代 $host:$server_port&#10;- 添加 X-Forwarded-Proto 头，增强安全性- 设置 proxy_connect_timeout 为300 秒，提高连接超时时间&#10;- 启用 HTTP/1.1 并禁用 chunked_transfer_encoding，优化数据传输">
      <option name="closed" value="true" />
      <created>1753856094721</created>
      <option name="number" value="00049" />
      <option name="presentableId" value="LOCAL-00049" />
      <option name="project" value="LOCAL" />
      <updated>1753856094721</updated>
    </task>
    <task id="LOCAL-00050" summary="feat(docker-prod): 优化 Minio 对象存储服务访问配置&#10;&#10;- 添加配置以支持 Minio 访问的特殊需求&#10;- 设置 ignore_invalid_headers 为 off，允许特殊字符的请求头&#10;- 移除上传文件大小限制- 关闭代理缓冲功能&#10;- 调整代码格式，提高可读性">
      <option name="closed" value="true" />
      <created>*************</created>
      <option name="number" value="00050" />
      <option name="presentableId" value="LOCAL-00050" />
      <option name="project" value="LOCAL" />
      <updated>*************</updated>
    </task>
    <task id="LOCAL-00051" summary="fix(view): 修复上传图片预览功能&#10;&#10;- 在 oilAccount 和 account 组件的 uploadFileModal 中&#10;- 调整了请求参数，移除了不必要的 baseUrl拼接&#10;- 更新了返回的 URL 处理方式，添加了 baseUrl">
      <option name="closed" value="true" />
      <created>*************</created>
      <option name="number" value="00051" />
      <option name="presentableId" value="LOCAL-00051" />
      <option name="project" value="LOCAL" />
      <updated>*************</updated>
    </task>
    <task id="LOCAL-00052" summary="fix(account): 修复上传文件预览功能&#10;&#10;- 在 oilAccount 和 account 组件中的 uploadFileModal.vue 文件中&#10;- 将 API 请求参数从 url 改为 objectName&#10;- 这个修改确保了正确传递文件对象名称以获取预览 URL">
      <option name="closed" value="true" />
      <created>*************</created>
      <option name="number" value="00052" />
      <option name="presentableId" value="LOCAL-00052" />
      <option name="project" value="LOCAL" />
      <updated>*************</updated>
    </task>
    <task id="LOCAL-00053" summary="perf(oss): 优化 MinIO 预览适配&#10;&#10;- 修改 Nginx 配置，设置正确的 Host 头以支持 MinIO 请求&#10;- 传递原始 Host 给后端，用于日志记录&#10;- 移除后端预览 URL 获取接口，直接在前端构建预览 URL&#10;-优化图片加载逻辑，提高预览速度">
      <option name="closed" value="true" />
      <created>*************</created>
      <option name="number" value="00053" />
      <option name="presentableId" value="LOCAL-00053" />
      <option name="project" value="LOCAL" />
      <updated>*************</updated>
    </task>
    <option name="localTasksCounter" value="54" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="OPEN_GENERIC_TABS">
      <map>
        <entry key="4354d0b1-7617-41cf-86d4-bf411dd012c6" value="TOOL_WINDOW" />
        <entry key="37a371a1-31b5-4f48-bb61-eeb6f8193fb0" value="TOOL_WINDOW" />
      </map>
    </option>
    <option name="RECENT_FILTERS">
      <map>
        <entry key="Branch">
          <value>
            <list>
              <RecentGroup>
                <option name="FILTER_VALUES">
                  <option value="origin/main_20250710" />
                </option>
              </RecentGroup>
              <RecentGroup>
                <option name="FILTER_VALUES">
                  <option value="origin/main_20250424" />
                </option>
              </RecentGroup>
            </list>
          </value>
        </entry>
        <entry key="User">
          <value>
            <list>
              <RecentGroup>
                <option name="FILTER_VALUES">
                  <option value="*" />
                </option>
              </RecentGroup>
            </list>
          </value>
        </entry>
      </map>
    </option>
    <option name="TAB_STATES">
      <map>
        <entry key="37a371a1-31b5-4f48-bb61-eeb6f8193fb0">
          <value>
            <State />
          </value>
        </entry>
        <entry key="4354d0b1-7617-41cf-86d4-bf411dd012c6">
          <value>
            <State />
          </value>
        </entry>
        <entry key="MAIN">
          <value>
            <State>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="origin/dev" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
            </State>
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <option name="CHECK_CODE_SMELLS_BEFORE_PROJECT_COMMIT" value="false" />
    <option name="CHECK_NEW_TODO" value="false" />
    <MESSAGE value="refactor(view): 优化首页数据展示&#10;&#10;- 移除用电量和生产用电数据中的toFixed(2)调用&#10;- 保留原始数据精度，避免不必要的小数位数" />
    <MESSAGE value="refactor(view): 修改考核部门列的显示内容&#10;&#10;- 将考核部门列的 prop 从 &quot;objectType&quot; 修改为 &quot;companyName&quot;- 移除了不必要的 slot 和 formatter 函数" />
    <MESSAGE value="build(deps): 优化 vue-loader-v16 依赖配置&#10;&#10;- 移除重复的 big.js、emojis-list、has-flag等依赖项&#10;- 调整 loader-utils 依赖版本&#10;- 删除不必要的嵌套依赖结构" />
    <MESSAGE value="feat(proxy): 添加能源接口服务配置并更新开发环境配置&#10;&#10;- 在生产环境和测试环境的 Nginx 配置中添加了 /energy-interface/ 服务的代理配置&#10;- 更新了 Vue 开发环境中的代理目标地址" />
    <MESSAGE value="refactor(view): 优化 countryModal 组件代码结构&#10;&#10;-调整代码缩进和格式，提高可读性" />
    <MESSAGE value="feat(station): 局站编辑框支持机房选择功能" />
    <MESSAGE value="feat(basedata): 添加在网表计清单功能" />
    <MESSAGE value="增加判断 基站及D类放行" />
    <MESSAGE value="增加判断 基站及D类放行时移除重复的错误提示逻辑" />
    <MESSAGE value="添加表计清单查询功能" />
    <MESSAGE value="refactor(view): 移除 meterinfoAllJt 组件中的加载指示器" />
    <MESSAGE value="refactor(view): 添加表计清单导入功能" />
    <MESSAGE value="feat(basedata): 添加表计清单查询相关权限控制" />
    <MESSAGE value="refactor(router): 移除在网表计数据清单路由" />
    <MESSAGE value="feat(docker): 添加 library-api接口代理配置" />
    <MESSAGE value="update" />
    <MESSAGE value="update package-lock.json" />
    <MESSAGE value="feat(docker-prod): 添加对象存储服务访问配置&#10;&#10;- 在 prod.conf 文件中添加了 /ln-nh-oss/ 位置的代理配置&#10;- 通过此配置，可以访问对象存储服务" />
    <MESSAGE value="fix(account): 修复在线预览文件路径问题&#10;&#10;- 在 uploadFileModal.vue 文件中，为适应 minio 迁移，增加了获取当前域名作为 baseUrl 的逻辑&#10;-将 row.url 前面加上 baseUrl，确保生成的预览 URL 是完整的&#10;- 这个修改同时应用于 oilAccount 和 account 模块中的 uploadFileModal 组件" />
    <MESSAGE value="refactor(docker-prod):调整 Nginx 配置&#10;&#10;- 移动对象存储服务配置到静态资源配置之前- 优化静态资源配置，使其更容易理解和维护&#10;- 删除重复的对象存储服务配置" />
    <MESSAGE value="perf(docker-prod): 优化对象存储服务访问配置&#10;&#10;- 修改 Host 头设置，使用 $http_host 替代 $host:$server_port&#10;- 添加 X-Forwarded-Proto 头，增强安全性- 设置 proxy_connect_timeout 为300 秒，提高连接超时时间&#10;- 启用 HTTP/1.1 并禁用 chunked_transfer_encoding，优化数据传输" />
    <MESSAGE value="feat(docker-prod): 优化 Minio 对象存储服务访问配置" />
    <MESSAGE value="fix(view): 修复上传图片预览功能&#10;&#10;- 在 oilAccount 和 account 组件的 uploadFileModal 中&#10;- 调整了请求参数，移除了不必要的 baseUrl拼接&#10;- 更新了返回的 URL 处理方式，添加了 baseUrl" />
    <MESSAGE value="fix(account): 修复上传文件预览功能&#10;&#10;- 在 oilAccount 和 account 组件中的 uploadFileModal.vue 文件中&#10;- 将 API 请求参数从 url 改为 objectName&#10;- 这个修改确保了正确传递文件对象名称以获取预览 URL" />
    <MESSAGE value="perf(oss): 优化 MinIO 预览适配&#10;&#10;- 修改 Nginx 配置，设置正确的 Host 头以支持 MinIO 请求&#10;- 传递原始 Host 给后端，用于日志记录&#10;- 移除后端预览 URL 获取接口，直接在前端构建预览 URL&#10;-优化图片加载逻辑，提高预览速度" />
    <option name="LAST_COMMIT_MESSAGE" value="perf(oss): 优化 MinIO 预览适配&#10;&#10;- 修改 Nginx 配置，设置正确的 Host 头以支持 MinIO 请求&#10;- 传递原始 Host 给后端，用于日志记录&#10;- 移除后端预览 URL 获取接口，直接在前端构建预览 URL&#10;-优化图片加载逻辑，提高预览速度" />
    <option name="NON_MODAL_COMMIT_POSTPONE_SLOW_CHECKS" value="false" />
  </component>
  <component name="XSLT-Support.FileAssociations.UIState">
    <expand />
    <select />
  </component>
</project>