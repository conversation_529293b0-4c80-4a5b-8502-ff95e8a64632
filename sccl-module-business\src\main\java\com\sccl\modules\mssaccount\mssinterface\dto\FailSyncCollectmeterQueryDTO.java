package com.sccl.modules.mssaccount.mssinterface.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 智能电表同步日志查询DTO
 *
 * <AUTHOR>
 * @date 2025-01-25
 */
@Data
public class FailSyncCollectmeterQueryDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 采集时间 */
    private String collectTime;

    /** 地市编码 */
    private String cityCode;

    /** 地市名称 */
    private String cityName;

    /** 区县编码 */
    private String countyCode;

    /** 区县名称 */
    private String countyName;

    /** 站址编码 */
    private String stationCode;

    /** 站址名称 */
    private String stationName;

    /** 同步标志 0未同步，1同步成功，2同步失败 */
    private Integer syncFlag;

    /** 账期 */
    private String budget;

    /** 开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date startTime;

    /** 结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date endTime;

    /** 查询类型：day-按天，month-按月 */
    private String queryType;

    /** 统计维度：用于按月统计查询 */
    private String statisticDimension;
}
