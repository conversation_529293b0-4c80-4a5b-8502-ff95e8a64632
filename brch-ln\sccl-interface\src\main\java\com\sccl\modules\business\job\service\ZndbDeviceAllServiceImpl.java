package com.sccl.modules.business.job.service;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;
import cn.hutool.http.Method;
import cn.hutool.json.JSONUtil;
import com.sccl.modules.business.job.domain.ZndbDeviceAll;
import com.sccl.modules.business.job.mapper.ZndbDeviceAllMapper;
import com.sccl.modules.business.job.vo.CollectZndbData;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class ZndbDeviceAllServiceImpl implements ZndbDeviceAllService {

    @Autowired(required = false)
    private ZndbDeviceAllMapper zndbDeviceAllMapper;

    //集团能耗同步上传地址
    @Value("${MssInterface.MssJsonClient.NHURL}")
    private String NHURL;
    @Value("${MssInterface.MssJsonClient.APPKey}")
    private String APPKey;
    @Value("${MssInterface.MssJsonClient.AppSecret}")
    private String AppSecret;
    @Value("${MssInterface.MssJsonClient.msgF}")
    private String msgF;

    @Override
    public void insert(ZndbDeviceAll zndbDeviceAll) {
        log.info("智能电表数据采集，收到数据：{}", zndbDeviceAll);
        zndbDeviceAll.setCreateTime(new Date());
        zndbDeviceAllMapper.addZndbDeviceAll(zndbDeviceAll);
    }

    @Override
    public void syncDate(String collectTime) {
        List<CollectZndbData> meterList = zndbDeviceAllMapper.syncDate(collectTime);
        log.info("开始同步:{}条数据", meterList.size());
        // 推送数据到集团
        String url = StrUtil.format("{}/nh.cj.xx/syncEnergyCollectionInfos", NHURL);
        Map<String, Object> param = new HashMap<>();
        param.put("infos", meterList);
        param.put("provinceCode", "26");
        param.put("msgId", StrUtil.format("{}{}", msgF, System.currentTimeMillis()));
        HttpRequest request = HttpUtil.createRequest(Method.POST, url);
        request.timeout(10 * 60 * 1000);
        request.header("Content-Type", "application/json");
        request.header("X-APP-ID", APPKey);
        request.header("X-APP-KEY", AppSecret);
        log.info("推送智能电表数据到集团：{}\n{}", url, param);
        String result = request.body(JSONUtil.toJsonStr(param)).execute().body();
        log.info("智能电表数据同步到集团结果:{}", result);
    }


}
