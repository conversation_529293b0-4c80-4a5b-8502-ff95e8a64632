目前已完成同步功能，当前接口地址为：/mssaccount/mssInterface/syncSmartMeterData
对应的实现类方法：public SyncSmartMeterDataResponse syncSmartMeterData(String collectTimeStr, Boolean updateCollectMeter)
1. 现在需要在同步集团接口后，完成日志记录功能，数据表我已经创建完成，你需要创建对应的实体类表
2. 前端页面有查看日志功能，支持按天、按月、成功失败、按月统计查询(以countyCode、stationCode为维度，合计该站址acData值)

要求
1. 后端写在 src/main/java/com/sccl/modules/mssaccount/mssinterface 目录下新建分层结构
2. 前端写在 E:\cl-project\ln-nenghao\brch-ln-vue\src\view\statistics 目录下
3. 读取数据库mcp，创建对应的菜单，上级目录为统计分析，下面提供了对应的菜单sql
```sql
INSERT INTO `rmp`.`sys_authorities` (`id`, `name`, `code`, `parent_id`, `idx_num`, `auth_url`, `auth_type`, `descn`, `visible`, `perms`, `icon`, `del_flag`, `creator_id`, `creator_name`, `create_time`, `update_by_id`, `update_by_name`, `update_time`, `remark`) VALUES (3849276838270636036, '统计分析', 'ROLE_TOOL', 0, 6, '/statistics', 'M', '系统工具目录', '0', '', 'ios-stats', '0', 3849276838241275904, 'admin', '2019-01-15 11:33:00', 3849276838241275904, 'admin', '2019-01-15 11:33:00', '');
```

## 表结构
```sql
CREATE TABLE `fail_sync_collectmeter_zndb` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `collectTime` int(11) DEFAULT NULL,
  `provinceCode` varchar(20) DEFAULT NULL,
  `cityCode` varchar(100) DEFAULT NULL,
  `cityName` varchar(100) DEFAULT NULL,
  `countyCode` varchar(100) DEFAULT NULL,
  `countyName` varchar(100) DEFAULT NULL,
  `stationCode` varchar(100) DEFAULT NULL,
  `stationName` varchar(200) DEFAULT NULL,
  `acData` float(10,2) DEFAULT NULL,
  `acDataSource` varchar(10) DEFAULT NULL,
  `oepgData` varchar(10) DEFAULT NULL,
  `oepgDataSource` varchar(10) DEFAULT NULL,
  `pvpgData` varchar(10) DEFAULT NULL,
  `pvpgDataSource` varchar(10) DEFAULT NULL,
  `parentStationCode` varchar(100) DEFAULT NULL,
  `ccoer` varchar(10) DEFAULT NULL,
  `cdcf` varchar(10) DEFAULT NULL,
  `energyData` varchar(10) DEFAULT NULL,
  `energyDataSource` varchar(10) DEFAULT NULL,
  `deviceData` varchar(10) DEFAULT NULL,
  `deviceDataSource` varchar(10) DEFAULT NULL,
  `productionData` varchar(10) DEFAULT NULL,
  `productionDataSource` varchar(10) DEFAULT NULL,
  `managementData` varchar(10) DEFAULT NULL,
  `managementDataSource` varchar(10) DEFAULT NULL,
  `businessData` varchar(10) DEFAULT NULL,
  `businessDataSource` varchar(10) DEFAULT NULL,
  `otherData` varchar(10) DEFAULT NULL,
  `otherDataSource` varchar(10) DEFAULT NULL,
  `del_flag` int(11) DEFAULT '0' COMMENT '数据有效标识 0/1 有效/无效',
  `syncFlag` tinyint(3) unsigned DEFAULT '0' COMMENT '同步标志 0未同步，1同步成功 2同步失败 ',
  `failMag` varchar(255) DEFAULT NULL COMMENT '失败原因',
  `retry` int(11) DEFAULT '0' COMMENT '重试次数',
  `sync_time` datetime DEFAULT NULL COMMENT '同步时间记录',
  `budget` varchar(20) DEFAULT NULL COMMENT '账期',
  PRIMARY KEY (`id`),
  KEY `index_stationcode` (`stationCode`),
  KEY `index_citycode` (`cityCode`),
  KEY `index_countrycode` (`countyCode`),
  KEY `index_1` (`budget`),
  KEY `id` (`id`),
  KEY `sync_time` (`sync_time`),
  KEY `collectTime` (`collectTime`),
  KEY `fail_sync_collectmeter_time_station_data_IDX` (`collectTime`,`stationCode`,`energyData`) USING BTREE,
  KEY `fail_sync_collectmeter_city_county_station` (`cityCode`,`countyCode`,`stationCode`) USING BTREE,
  KEY `fail_sync_collectmeter_sort_IDX` (`collectTime`,`cityCode`,`countyCode`,`stationCode`,`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=53862244 DEFAULT CHARSET=utf8 COMMENT='智能电表采集失败表';
```