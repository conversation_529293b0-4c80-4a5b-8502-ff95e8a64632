13:31:28.691 [main] INFO  o.s.b.d.r.RestartApplicationListener - [onApplicationStartingEvent,88] - <PERSON><PERSON> disabled due to an agent-based reloader being active
13:31:29.134 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.1.7.Final
13:31:29.428 [main] INFO  com.sccl.Application - [logStarting,55] - Starting Application using Java 1.8.0_421 on WIN-TVKN4A60TS0 with PID 32320 (E:\cl-project\ln-nenghao\brch-ln\sccl-web\target\classes started by Administrator in E:\cl-project\ln-nenghao\brch-ln)
13:31:29.429 [main] INFO  com.sccl.Application - [logStartupProfileInfo,668] - The following profiles are active: dev-ln
13:31:29.497 [main] INFO  o.s.b.d.r.ChangeableUrls - [logTo,255] - The Class-Path manifest attribute in E:\.m2\repository\com\sun\xml\ws\jaxws-tools\2.2.6\jaxws-tools-2.2.6.jar referenced one or more files that do not exist: file:/E:/.m2/repository/com/sun/xml/ws/jaxws-tools/2.2.6/jaxws-rt.jar,file:/E:/.m2/repository/com/sun/xml/ws/jaxws-tools/2.2.6/jaxb-xjc.jar
13:31:29.498 [main] INFO  o.s.b.d.e.DevToolsPropertyDefaultsPostProcessor - [logTo,255] - Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
13:31:29.498 [main] INFO  o.s.b.d.e.DevToolsPropertyDefaultsPostProcessor - [logTo,255] - For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
13:31:31.880 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - [multipleStoresDetected,250] - Multiple Spring Data modules found, entering strict repository configuration mode!
13:31:31.881 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - [registerRepositoriesIn,128] - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
13:31:32.152 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - [registerRepositoriesIn,188] - Finished Spring Data repository scanning in 256 ms. Found 0 JPA repository interfaces.
13:31:32.173 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - [multipleStoresDetected,250] - Multiple Spring Data modules found, entering strict repository configuration mode!
13:31:32.174 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - [registerRepositoriesIn,128] - Bootstrapping Spring Data MongoDB repositories in DEFAULT mode.
13:31:32.324 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - [registerRepositoriesIn,188] - Finished Spring Data repository scanning in 150 ms. Found 0 MongoDB repository interfaces.
13:31:32.339 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - [multipleStoresDetected,250] - Multiple Spring Data modules found, entering strict repository configuration mode!
13:31:32.340 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - [registerRepositoriesIn,128] - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
13:31:32.512 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - [registerRepositoriesIn,188] - Finished Spring Data repository scanning in 161 ms. Found 0 Redis repository interfaces.
13:31:33.027 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - MPJ SqlSessionFactory bean definition: sqlSessionFactory factoryBeanName: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration factoryMethodName: sqlSessionFactory source: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration.sqlSessionFactory(javax.sql.DataSource)
13:31:33.097 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'shiroConfig' of type [com.sccl.config.ShiroConfig$$EnhancerBySpringCGLIB$$1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
13:31:33.117 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'getEhCacheManager' of type [org.apache.shiro.cache.ehcache.EhCacheManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
13:31:33.180 [main] INFO  o.a.s.c.e.EhCacheManager - [getCache,158] - Cache with name 'com.sccl.common.shiro.jwt.realm.UserRealm.authorizationCache' does not yet exist.  Creating now.
13:31:33.181 [main] INFO  o.a.s.c.e.EhCacheManager - [getCache,165] - Added EhCache named [com.sccl.common.shiro.jwt.realm.UserRealm.authorizationCache]
13:31:33.184 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'userRealm' of type [com.sccl.common.shiro.jwt.realm.UserRealm] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
13:31:33.201 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'securityManager' of type [org.apache.shiro.web.mgt.DefaultWebSecurityManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
13:31:33.204 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'captchaValidateFilter' of type [com.sccl.common.shiro.web.filter.captcha.CaptchaValidateFilter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
13:31:33.232 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'authorizationAttributeSourceAdvisor' of type [org.apache.shiro.spring.security.interceptor.AuthorizationAttributeSourceAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
13:31:33.427 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'druidMutilConfig' of type [com.sccl.framework.config.DruidMutilConfig$$EnhancerBySpringCGLIB$$1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
13:31:33.473 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'bsConfig' of type [com.sccl.framework.config.BsConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
13:31:33.495 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'ecmDsProps' of type [org.springframework.boot.autoconfigure.jdbc.DataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
13:31:33.531 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'dataSourceProperties' of type [com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceWrapper] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
13:31:33.622 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'dynamicDataSource' of type [com.sccl.framework.datasource.DynamicDataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
13:31:33.627 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'spring.jpa-org.springframework.boot.autoconfigure.orm.jpa.JpaProperties' of type [org.springframework.boot.autoconfigure.orm.jpa.JpaProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
13:31:33.631 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'spring.jpa.hibernate-org.springframework.boot.autoconfigure.orm.jpa.HibernateProperties' of type [org.springframework.boot.autoconfigure.orm.jpa.HibernateProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
13:31:33.638 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaConfiguration' of type [org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
13:31:33.644 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration' of type [org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
13:31:33.650 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'spring.transaction-org.springframework.boot.autoconfigure.transaction.TransactionProperties' of type [org.springframework.boot.autoconfigure.transaction.TransactionProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
13:31:33.651 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'platformTransactionManagerCustomizers' of type [org.springframework.boot.autoconfigure.transaction.TransactionManagerCustomizers] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
13:31:33.660 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'org.springframework.boot.autoconfigure.cache.RedisCacheConfiguration' of type [org.springframework.boot.autoconfigure.cache.RedisCacheConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
13:31:33.664 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'spring.cache-org.springframework.boot.autoconfigure.cache.CacheProperties' of type [org.springframework.boot.autoconfigure.cache.CacheProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
13:31:33.666 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'org.springframework.boot.autoconfigure.cache.CacheAutoConfiguration' of type [org.springframework.boot.autoconfigure.cache.CacheAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
13:31:33.669 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'cacheManagerCustomizers' of type [org.springframework.boot.autoconfigure.cache.CacheManagerCustomizers] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
13:31:33.678 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'spring.redis-org.springframework.boot.autoconfigure.data.redis.RedisProperties' of type [org.springframework.boot.autoconfigure.data.redis.RedisProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
13:31:33.681 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration' of type [org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
13:31:33.774 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'lettuceClientResources' of type [io.lettuce.core.resource.DefaultClientResources] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
13:31:33.839 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'redisConnectionFactory' of type [org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
13:31:33.858 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'cacheManager' of type [org.springframework.data.redis.cache.RedisCacheManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
13:31:33.872 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'jpaVendorAdapter' of type [org.springframework.orm.jpa.vendor.HibernateJpaVendorAdapter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
13:31:33.876 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'entityManagerFactoryBuilder' of type [org.springframework.boot.orm.jpa.EntityManagerFactoryBuilder] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
13:31:34.986 [main] INFO  c.a.d.p.DruidDataSource - [init,983] - {dataSource-1} inited
13:31:35.256 [main] INFO  o.h.j.i.u.LogHelper - [logPersistenceUnitInformation,31] - HHH000204: Processing PersistenceUnitInfo [name: default]
13:31:35.308 [main] INFO  o.hibernate.Version - [logVersion,44] - HHH000412: Hibernate ORM core version 5.4.32.Final
13:31:35.488 [main] INFO  o.h.a.common.Version - [<clinit>,56] - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
13:31:35.606 [main] INFO  o.h.dialect.Dialect - [<init>,175] - HHH000400: Using dialect: org.hibernate.dialect.MySQL8Dialect
13:31:35.853 [main] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - [initiateService,52] - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
13:31:35.869 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - [buildNativeEntityManagerFactory,437] - Initialized JPA EntityManagerFactory for persistence unit 'default'
13:31:35.907 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'entityManagerFactory' of type [org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
13:31:35.917 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'entityManagerFactory' of type [com.sun.proxy.$Proxy123] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
13:31:35.924 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'transactionManager' of type [org.springframework.orm.jpa.JpaTransactionManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
13:31:35.928 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'txAdviceInterceptor' of type [com.sccl.framework.common.transaction.TxAdviceInterceptor$$EnhancerBySpringCGLIB$$1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
13:31:35.948 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'txAdvice' of type [org.springframework.transaction.interceptor.TransactionInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
13:31:35.948 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'txAdviceAdvisor' of type [org.springframework.aop.support.DefaultPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
13:31:36.446 [main] INFO  o.s.b.w.e.t.TomcatWebServer - [initialize,108] - Tomcat initialized with port(s): 8080 (http)
13:31:36.466 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-8080"]
13:31:36.466 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
13:31:36.466 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.55]
13:31:36.609 [main] INFO  o.a.c.c.C.[.[.[/energy-cost] - [log,173] - Initializing Spring embedded WebApplicationContext
13:31:36.609 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - [prepareWebApplicationContext,289] - Root WebApplicationContext: initialization completed in 7111 ms
13:31:36.873 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - mybatis plus join properties config complete
13:31:36.927 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - mybatis plus join SqlInjector init
13:31:37.339 [main] INFO  o.d.c.k.b.i.ClasspathKieProject - [notifyKieModuleFound,152] - Found kmodule: file:/E:/cl-project/ln-nenghao/brch-ln/sccl-web/target/classes/META-INF/kmodule.xml
13:31:37.544 [main] INFO  o.d.c.k.b.i.ClasspathKieProject - [generatePomPropertiesFromPom,376] - Recursed up folders, found and used pom.xml E:\cl-project\ln-nenghao\brch-ln\sccl-web\pom.xml
13:31:39.026 [main] INFO  c.s.m.b.s.u.StationAuditUtil - [<clinit>,76] - accountService getBean is null
13:31:39.128 [main] INFO  c.s.t.f.u.i.PropertiesHolder - [load,80] - 加载资源文件：timing-config-protocol.yml
13:31:39.152 [main] INFO  c.s.t.f.u.i.PropertiesHolder - [load,80] - 加载资源文件：timing-config-sta.yml
13:31:41.693 [main] INFO  o.a.s.c.e.EhCacheManager - [getCache,169] - Using existing EHCache named [loginRecordCache]
13:31:42.282 [main] INFO  c.s.m.s.a.c.LocalCacheConfig - [localCacheManager,48] - 本地缓存注入完成，开启权限缓存，本地缓存长度：1000，过期时间：1800000min，策略：only_local
13:31:50.228 [main] INFO  c.s.t.f.u.i.PropertiesHolder - [load,80] - 加载资源文件：timing-config-sta.yml
13:31:52.201 [main] INFO  o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - [lambda$buildAutowiringMetadata$1,478] - Autowired annotation is not supported on static fields: private static org.springframework.web.client.RestTemplate com.sccl.modules.util.WorkflowUtil.restTemplate
13:31:52.856 [main] INFO  s.d.s.w.PropertySourcedRequestMappingHandlerMapping - [initHandlerMethods,69] - Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2Controller#getDocumentation(String, HttpServletRequest)]
13:31:53.058 [main] INFO  o.s.b.d.a.OptionalLiveReloadServer - [startServer,58] - LiveReload server is running on port 35729
13:31:53.105 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - [<init>,53] - Adding welcome page: class path resource [META-INF/resources/index.html]
13:31:54.090 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-8080"]
13:31:54.115 [main] INFO  o.s.b.w.e.t.TomcatWebServer - [start,220] - Tomcat started on port(s): 8080 (http) with context path '/energy-cost'
13:31:54.116 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - [start,147] - Context refreshed
13:31:54.140 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - [start,150] - Found 5 custom documentation plugin(s)
13:31:54.512 [main] INFO  s.d.s.w.s.ApiListingReferenceScanner - [scan,41] - Scanning for api listing references
13:31:55.028 [main] INFO  s.d.s.w.s.ApiListingReferenceScanner - [scan,41] - Scanning for api listing references
13:31:55.329 [main] INFO  s.d.s.w.s.ApiListingReferenceScanner - [scan,41] - Scanning for api listing references
13:31:55.621 [main] INFO  s.d.s.w.s.ApiListingReferenceScanner - [scan,41] - Scanning for api listing references
13:31:55.655 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingGET_1
13:31:55.687 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingGET_1
13:31:55.707 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addSaveUsingPOST_1
13:31:55.717 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingGET_2
13:31:55.722 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editSaveUsingPOST_1
13:31:55.730 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getUserInfoUsingGET_1
13:31:55.736 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_1
13:31:55.738 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingHEAD_1
13:31:55.740 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingPOST_1
13:31:55.742 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingPUT_1
13:31:55.744 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingPATCH_1
13:31:55.746 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingDELETE_1
13:31:55.748 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingOPTIONS_1
13:31:55.750 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingTRACE_1
13:31:55.753 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_1
13:31:55.756 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: resetPwdUsingGET_1
13:31:55.760 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: resetPwdUsingPOST_1
13:31:56.082 [main] INFO  s.d.s.w.s.ApiListingReferenceScanner - [scan,41] - Scanning for api listing references
13:31:56.100 [main] INFO  com.sccl.Application - [logStarted,61] - Started Application in 27.426 seconds (JVM running for 30.928)
13:34:25.539 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[.[.[/energy-cost] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
13:34:25.539 [http-nio-8080-exec-1] INFO  o.s.w.s.DispatcherServlet - [initServletBean,525] - Initializing Servlet 'dispatcherServlet'
13:34:25.543 [http-nio-8080-exec-1] INFO  o.s.w.s.DispatcherServlet - [initServletBean,547] - Completed initialization in 4 ms
13:46:23.727 [http-nio-8080-exec-26] INFO  c.s.m.b.a.c.AmmeterorprotocolController - [list,111] - 协议管理查询
13:46:24.562 [http-nio-8080-exec-26] INFO  c.s.m.b.a.c.AmmeterorprotocolController - [list,119] - 协议管理查询结束
13:46:27.173 [http-nio-8080-exec-32] INFO  c.s.m.b.a.c.AmmeterorprotocolController - [list,111] - 协议管理查询
13:46:27.376 [http-nio-8080-exec-32] INFO  c.s.m.b.a.c.AmmeterorprotocolController - [list,119] - 协议管理查询结束
14:05:09.245 [SpringContextShutdownHook] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - [destroy,651] - Closing JPA EntityManagerFactory for persistence unit 'default'
14:05:09.948 [SpringContextShutdownHook] INFO  o.h.j.i.u.LogHelper - [logPersistenceUnitInformation,31] - HHH000204: Processing PersistenceUnitInfo [name: default]
14:05:10.037 [SpringContextShutdownHook] INFO  o.h.dialect.Dialect - [<init>,175] - HHH000400: Using dialect: org.hibernate.dialect.MySQL8Dialect
14:05:10.051 [SpringContextShutdownHook] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - [initiateService,52] - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
14:05:10.188 [SpringContextShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2024] - {dataSource-0} closing ...
14:05:16.918 [main] INFO  o.s.b.d.r.RestartApplicationListener - [onApplicationStartingEvent,88] - Restart disabled due to an agent-based reloader being active
14:05:17.299 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.1.7.Final
14:05:17.578 [main] INFO  com.sccl.Application - [logStarting,55] - Starting Application using Java 1.8.0_421 on WIN-TVKN4A60TS0 with PID 30708 (E:\cl-project\ln-nenghao\brch-ln\sccl-web\target\classes started by Administrator in E:\cl-project\ln-nenghao\brch-ln)
14:05:17.579 [main] INFO  com.sccl.Application - [logStartupProfileInfo,668] - The following profiles are active: dev-ln
14:05:17.646 [main] INFO  o.s.b.d.r.ChangeableUrls - [logTo,255] - The Class-Path manifest attribute in E:\.m2\repository\com\sun\xml\ws\jaxws-tools\2.2.6\jaxws-tools-2.2.6.jar referenced one or more files that do not exist: file:/E:/.m2/repository/com/sun/xml/ws/jaxws-tools/2.2.6/jaxws-rt.jar,file:/E:/.m2/repository/com/sun/xml/ws/jaxws-tools/2.2.6/jaxb-xjc.jar
14:05:17.646 [main] INFO  o.s.b.d.e.DevToolsPropertyDefaultsPostProcessor - [logTo,255] - Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
14:05:17.646 [main] INFO  o.s.b.d.e.DevToolsPropertyDefaultsPostProcessor - [logTo,255] - For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
14:05:19.843 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - [multipleStoresDetected,250] - Multiple Spring Data modules found, entering strict repository configuration mode!
14:05:19.844 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - [registerRepositoriesIn,128] - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
14:05:20.068 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - [registerRepositoriesIn,188] - Finished Spring Data repository scanning in 213 ms. Found 0 JPA repository interfaces.
14:05:20.088 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - [multipleStoresDetected,250] - Multiple Spring Data modules found, entering strict repository configuration mode!
14:05:20.089 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - [registerRepositoriesIn,128] - Bootstrapping Spring Data MongoDB repositories in DEFAULT mode.
14:05:20.221 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - [registerRepositoriesIn,188] - Finished Spring Data repository scanning in 132 ms. Found 0 MongoDB repository interfaces.
14:05:20.232 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - [multipleStoresDetected,250] - Multiple Spring Data modules found, entering strict repository configuration mode!
14:05:20.234 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - [registerRepositoriesIn,128] - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
14:05:20.371 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - [registerRepositoriesIn,188] - Finished Spring Data repository scanning in 131 ms. Found 0 Redis repository interfaces.
14:05:20.834 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - MPJ SqlSessionFactory bean definition: sqlSessionFactory factoryBeanName: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration factoryMethodName: sqlSessionFactory source: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration.sqlSessionFactory(javax.sql.DataSource)
14:05:20.894 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'shiroConfig' of type [com.sccl.config.ShiroConfig$$EnhancerBySpringCGLIB$$1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
14:05:20.913 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'getEhCacheManager' of type [org.apache.shiro.cache.ehcache.EhCacheManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
14:05:20.961 [main] INFO  o.a.s.c.e.EhCacheManager - [getCache,158] - Cache with name 'com.sccl.common.shiro.jwt.realm.UserRealm.authorizationCache' does not yet exist.  Creating now.
14:05:20.961 [main] INFO  o.a.s.c.e.EhCacheManager - [getCache,165] - Added EhCache named [com.sccl.common.shiro.jwt.realm.UserRealm.authorizationCache]
14:05:20.963 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'userRealm' of type [com.sccl.common.shiro.jwt.realm.UserRealm] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
14:05:20.978 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'securityManager' of type [org.apache.shiro.web.mgt.DefaultWebSecurityManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
14:05:20.980 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'captchaValidateFilter' of type [com.sccl.common.shiro.web.filter.captcha.CaptchaValidateFilter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
14:05:21.004 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'authorizationAttributeSourceAdvisor' of type [org.apache.shiro.spring.security.interceptor.AuthorizationAttributeSourceAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
14:05:21.167 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'druidMutilConfig' of type [com.sccl.framework.config.DruidMutilConfig$$EnhancerBySpringCGLIB$$1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
14:05:21.207 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'bsConfig' of type [com.sccl.framework.config.BsConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
14:05:21.228 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'ecmDsProps' of type [org.springframework.boot.autoconfigure.jdbc.DataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
14:05:21.262 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'dataSourceProperties' of type [com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceWrapper] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
14:05:21.343 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'dynamicDataSource' of type [com.sccl.framework.datasource.DynamicDataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
14:05:21.348 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'spring.jpa-org.springframework.boot.autoconfigure.orm.jpa.JpaProperties' of type [org.springframework.boot.autoconfigure.orm.jpa.JpaProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
14:05:21.352 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'spring.jpa.hibernate-org.springframework.boot.autoconfigure.orm.jpa.HibernateProperties' of type [org.springframework.boot.autoconfigure.orm.jpa.HibernateProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
14:05:21.358 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaConfiguration' of type [org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
14:05:21.364 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration' of type [org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
14:05:21.369 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'spring.transaction-org.springframework.boot.autoconfigure.transaction.TransactionProperties' of type [org.springframework.boot.autoconfigure.transaction.TransactionProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
14:05:21.371 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'platformTransactionManagerCustomizers' of type [org.springframework.boot.autoconfigure.transaction.TransactionManagerCustomizers] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
14:05:21.379 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'org.springframework.boot.autoconfigure.cache.RedisCacheConfiguration' of type [org.springframework.boot.autoconfigure.cache.RedisCacheConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
14:05:21.382 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'spring.cache-org.springframework.boot.autoconfigure.cache.CacheProperties' of type [org.springframework.boot.autoconfigure.cache.CacheProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
14:05:21.384 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'org.springframework.boot.autoconfigure.cache.CacheAutoConfiguration' of type [org.springframework.boot.autoconfigure.cache.CacheAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
14:05:21.387 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'cacheManagerCustomizers' of type [org.springframework.boot.autoconfigure.cache.CacheManagerCustomizers] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
14:05:21.396 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'spring.redis-org.springframework.boot.autoconfigure.data.redis.RedisProperties' of type [org.springframework.boot.autoconfigure.data.redis.RedisProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
14:05:21.399 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration' of type [org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
14:05:21.465 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'lettuceClientResources' of type [io.lettuce.core.resource.DefaultClientResources] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
14:05:21.510 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'redisConnectionFactory' of type [org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
14:05:21.526 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'cacheManager' of type [org.springframework.data.redis.cache.RedisCacheManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
14:05:21.538 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'jpaVendorAdapter' of type [org.springframework.orm.jpa.vendor.HibernateJpaVendorAdapter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
14:05:21.543 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'entityManagerFactoryBuilder' of type [org.springframework.boot.orm.jpa.EntityManagerFactoryBuilder] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
14:05:22.530 [main] INFO  c.a.d.p.DruidDataSource - [init,983] - {dataSource-1} inited
14:05:22.765 [main] INFO  o.h.j.i.u.LogHelper - [logPersistenceUnitInformation,31] - HHH000204: Processing PersistenceUnitInfo [name: default]
14:05:22.816 [main] INFO  o.hibernate.Version - [logVersion,44] - HHH000412: Hibernate ORM core version 5.4.32.Final
14:05:23.004 [main] INFO  o.h.a.common.Version - [<clinit>,56] - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
14:05:23.117 [main] INFO  o.h.dialect.Dialect - [<init>,175] - HHH000400: Using dialect: org.hibernate.dialect.MySQL8Dialect
14:05:23.391 [main] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - [initiateService,52] - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
14:05:23.408 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - [buildNativeEntityManagerFactory,437] - Initialized JPA EntityManagerFactory for persistence unit 'default'
14:05:23.442 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'entityManagerFactory' of type [org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
14:05:23.452 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'entityManagerFactory' of type [com.sun.proxy.$Proxy123] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
14:05:23.458 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'transactionManager' of type [org.springframework.orm.jpa.JpaTransactionManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
14:05:23.463 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'txAdviceInterceptor' of type [com.sccl.framework.common.transaction.TxAdviceInterceptor$$EnhancerBySpringCGLIB$$1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
14:05:23.483 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'txAdvice' of type [org.springframework.transaction.interceptor.TransactionInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
14:05:23.484 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'txAdviceAdvisor' of type [org.springframework.aop.support.DefaultPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
14:05:23.951 [main] INFO  o.s.b.w.e.t.TomcatWebServer - [initialize,108] - Tomcat initialized with port(s): 8080 (http)
14:05:23.972 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-8080"]
14:05:23.972 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:05:23.972 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.55]
14:05:24.102 [main] INFO  o.a.c.c.C.[.[.[/energy-cost] - [log,173] - Initializing Spring embedded WebApplicationContext
14:05:24.102 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - [prepareWebApplicationContext,289] - Root WebApplicationContext: initialization completed in 6454 ms
14:05:24.343 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - mybatis plus join properties config complete
14:05:24.390 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - mybatis plus join SqlInjector init
14:05:24.774 [main] INFO  o.d.c.k.b.i.ClasspathKieProject - [notifyKieModuleFound,152] - Found kmodule: file:/E:/cl-project/ln-nenghao/brch-ln/sccl-web/target/classes/META-INF/kmodule.xml
14:05:24.949 [main] INFO  o.d.c.k.b.i.ClasspathKieProject - [generatePomPropertiesFromPom,376] - Recursed up folders, found and used pom.xml E:\cl-project\ln-nenghao\brch-ln\sccl-web\pom.xml
14:05:26.308 [main] INFO  c.s.m.b.s.u.StationAuditUtil - [<clinit>,76] - accountService getBean is null
14:05:26.397 [main] INFO  c.s.t.f.u.i.PropertiesHolder - [load,80] - 加载资源文件：timing-config-protocol.yml
14:05:26.420 [main] INFO  c.s.t.f.u.i.PropertiesHolder - [load,80] - 加载资源文件：timing-config-sta.yml
14:05:28.867 [main] INFO  o.a.s.c.e.EhCacheManager - [getCache,169] - Using existing EHCache named [loginRecordCache]
14:05:37.293 [main] INFO  c.s.t.f.u.i.PropertiesHolder - [load,80] - 加载资源文件：timing-config-sta.yml
14:05:41.170 [main] INFO  o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - [lambda$buildAutowiringMetadata$1,478] - Autowired annotation is not supported on static fields: private static org.springframework.web.client.RestTemplate com.sccl.modules.util.WorkflowUtil.restTemplate
14:05:42.506 [main] INFO  s.d.s.w.PropertySourcedRequestMappingHandlerMapping - [initHandlerMethods,69] - Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2Controller#getDocumentation(String, HttpServletRequest)]
14:05:42.870 [main] INFO  o.s.b.d.a.OptionalLiveReloadServer - [startServer,58] - LiveReload server is running on port 35729
14:05:42.953 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - [<init>,53] - Adding welcome page: class path resource [META-INF/resources/index.html]
14:05:44.671 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-8080"]
14:05:44.703 [main] INFO  o.s.b.w.e.t.TomcatWebServer - [start,220] - Tomcat started on port(s): 8080 (http) with context path '/energy-cost'
14:05:44.705 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - [start,147] - Context refreshed
14:05:44.727 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - [start,150] - Found 5 custom documentation plugin(s)
14:05:45.473 [main] INFO  s.d.s.w.s.ApiListingReferenceScanner - [scan,41] - Scanning for api listing references
14:05:46.619 [main] INFO  s.d.s.w.s.ApiListingReferenceScanner - [scan,41] - Scanning for api listing references
14:05:47.306 [main] INFO  s.d.s.w.s.ApiListingReferenceScanner - [scan,41] - Scanning for api listing references
14:05:47.984 [main] INFO  s.d.s.w.s.ApiListingReferenceScanner - [scan,41] - Scanning for api listing references
14:05:48.051 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingGET_1
14:05:48.129 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingGET_1
14:05:48.177 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addSaveUsingPOST_1
14:05:48.201 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingGET_2
14:05:48.212 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editSaveUsingPOST_1
14:05:48.285 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getUserInfoUsingGET_1
14:05:48.298 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_1
14:05:48.302 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingHEAD_1
14:05:48.306 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingPOST_1
14:05:48.309 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingPUT_1
14:05:48.313 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingPATCH_1
14:05:48.317 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingDELETE_1
14:05:48.321 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingOPTIONS_1
14:05:48.324 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingTRACE_1
14:05:48.328 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_1
14:05:48.337 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: resetPwdUsingGET_1
14:05:48.348 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: resetPwdUsingPOST_1
14:05:49.206 [main] INFO  s.d.s.w.s.ApiListingReferenceScanner - [scan,41] - Scanning for api listing references
14:05:49.242 [main] INFO  com.sccl.Application - [logStarted,61] - Started Application in 32.339 seconds (JVM running for 35.732)
14:06:13.149 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[.[.[/energy-cost] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:06:13.151 [http-nio-8080-exec-1] INFO  o.s.w.s.DispatcherServlet - [initServletBean,525] - Initializing Servlet 'dispatcherServlet'
14:06:13.153 [http-nio-8080-exec-1] INFO  o.s.w.s.DispatcherServlet - [initServletBean,547] - Completed initialization in 2 ms
14:06:15.606 [http-nio-8080-exec-3] INFO  c.s.c.s.j.r.UserRealm - [doGetAuthorizationInfo,90] - 用户：libingran-李冰然权限过期时间：-2
14:06:15.606 [http-nio-8080-exec-2] INFO  c.s.c.s.j.r.UserRealm - [doGetAuthorizationInfo,90] - 用户：libingran-李冰然权限过期时间：-2
14:06:26.299 [http-nio-8080-exec-6] INFO  c.s.m.s.a.c.AuthController - [ajaxLogin,309] - 用户：libingran-李冰然权限信息共103条已存入Redis，过期时间：************
14:06:26.321 [http-nio-8080-exec-6] INFO  c.s.c.hlog.HLogUtils - [writeLoginLog,121] - {"server":"sysPA","sysCode":"S000AN2021012048","remoteIp":"127.0.0.1","loginTime":"2025-08-04 14:06:26.310","logAppId":"QX661449_scnh_NHF_sys_PA","serialNbr":"202508044287586310","loginStatus":"1","authType":"11","account":"libingran","userCode":"libingran"}
14:06:27.349 [http-nio-8080-exec-9] INFO  c.s.c.s.j.r.UserRealm - [doGetAuthorizationInfo,90] - 用户：libingran-李冰然权限过期时间：************
14:06:27.350 [http-nio-8080-exec-9] INFO  c.s.c.s.j.r.UserRealm - [doGetAuthorizationInfo,102] - 调用鉴权方法，获取到的权限条数103
14:08:50.514 [SpringContextShutdownHook] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - [destroy,651] - Closing JPA EntityManagerFactory for persistence unit 'default'
14:08:50.649 [SpringContextShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2024] - {dataSource-0} closing ...
14:08:56.710 [main] INFO  o.s.b.d.r.RestartApplicationListener - [onApplicationStartingEvent,88] - Restart disabled due to an agent-based reloader being active
14:08:57.073 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.1.7.Final
14:08:57.332 [main] INFO  com.sccl.Application - [logStarting,55] - Starting Application using Java 1.8.0_421 on WIN-TVKN4A60TS0 with PID 29204 (E:\cl-project\ln-nenghao\brch-ln\sccl-web\target\classes started by Administrator in E:\cl-project\ln-nenghao\brch-ln)
14:08:57.332 [main] INFO  com.sccl.Application - [logStartupProfileInfo,668] - The following profiles are active: dev-ln
14:08:57.392 [main] INFO  o.s.b.d.r.ChangeableUrls - [logTo,255] - The Class-Path manifest attribute in E:\.m2\repository\com\sun\xml\ws\jaxws-tools\2.2.6\jaxws-tools-2.2.6.jar referenced one or more files that do not exist: file:/E:/.m2/repository/com/sun/xml/ws/jaxws-tools/2.2.6/jaxws-rt.jar,file:/E:/.m2/repository/com/sun/xml/ws/jaxws-tools/2.2.6/jaxb-xjc.jar
14:08:57.392 [main] INFO  o.s.b.d.e.DevToolsPropertyDefaultsPostProcessor - [logTo,255] - Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
14:08:57.393 [main] INFO  o.s.b.d.e.DevToolsPropertyDefaultsPostProcessor - [logTo,255] - For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
14:08:59.482 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - [multipleStoresDetected,250] - Multiple Spring Data modules found, entering strict repository configuration mode!
14:08:59.483 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - [registerRepositoriesIn,128] - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
14:08:59.709 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - [registerRepositoriesIn,188] - Finished Spring Data repository scanning in 214 ms. Found 0 JPA repository interfaces.
14:08:59.730 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - [multipleStoresDetected,250] - Multiple Spring Data modules found, entering strict repository configuration mode!
14:08:59.730 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - [registerRepositoriesIn,128] - Bootstrapping Spring Data MongoDB repositories in DEFAULT mode.
14:08:59.869 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - [registerRepositoriesIn,188] - Finished Spring Data repository scanning in 138 ms. Found 0 MongoDB repository interfaces.
14:08:59.881 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - [multipleStoresDetected,250] - Multiple Spring Data modules found, entering strict repository configuration mode!
14:08:59.881 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - [registerRepositoriesIn,128] - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
14:09:00.024 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - [registerRepositoriesIn,188] - Finished Spring Data repository scanning in 136 ms. Found 0 Redis repository interfaces.
14:09:00.614 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - MPJ SqlSessionFactory bean definition: sqlSessionFactory factoryBeanName: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration factoryMethodName: sqlSessionFactory source: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration.sqlSessionFactory(javax.sql.DataSource)
14:09:00.678 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'shiroConfig' of type [com.sccl.config.ShiroConfig$$EnhancerBySpringCGLIB$$1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
14:09:00.697 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'getEhCacheManager' of type [org.apache.shiro.cache.ehcache.EhCacheManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
14:09:00.744 [main] INFO  o.a.s.c.e.EhCacheManager - [getCache,158] - Cache with name 'com.sccl.common.shiro.jwt.realm.UserRealm.authorizationCache' does not yet exist.  Creating now.
14:09:00.745 [main] INFO  o.a.s.c.e.EhCacheManager - [getCache,165] - Added EhCache named [com.sccl.common.shiro.jwt.realm.UserRealm.authorizationCache]
14:09:00.747 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'userRealm' of type [com.sccl.common.shiro.jwt.realm.UserRealm] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
14:09:00.761 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'securityManager' of type [org.apache.shiro.web.mgt.DefaultWebSecurityManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
14:09:00.762 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'captchaValidateFilter' of type [com.sccl.common.shiro.web.filter.captcha.CaptchaValidateFilter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
14:09:00.787 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'authorizationAttributeSourceAdvisor' of type [org.apache.shiro.spring.security.interceptor.AuthorizationAttributeSourceAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
14:09:00.950 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'druidMutilConfig' of type [com.sccl.framework.config.DruidMutilConfig$$EnhancerBySpringCGLIB$$1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
14:09:00.990 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'bsConfig' of type [com.sccl.framework.config.BsConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
14:09:01.012 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'ecmDsProps' of type [org.springframework.boot.autoconfigure.jdbc.DataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
14:09:01.045 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'dataSourceProperties' of type [com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceWrapper] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
14:09:01.125 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'dynamicDataSource' of type [com.sccl.framework.datasource.DynamicDataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
14:09:01.131 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'spring.jpa-org.springframework.boot.autoconfigure.orm.jpa.JpaProperties' of type [org.springframework.boot.autoconfigure.orm.jpa.JpaProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
14:09:01.135 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'spring.jpa.hibernate-org.springframework.boot.autoconfigure.orm.jpa.HibernateProperties' of type [org.springframework.boot.autoconfigure.orm.jpa.HibernateProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
14:09:01.141 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaConfiguration' of type [org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
14:09:01.145 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration' of type [org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
14:09:01.151 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'spring.transaction-org.springframework.boot.autoconfigure.transaction.TransactionProperties' of type [org.springframework.boot.autoconfigure.transaction.TransactionProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
14:09:01.152 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'platformTransactionManagerCustomizers' of type [org.springframework.boot.autoconfigure.transaction.TransactionManagerCustomizers] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
14:09:01.159 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'org.springframework.boot.autoconfigure.cache.RedisCacheConfiguration' of type [org.springframework.boot.autoconfigure.cache.RedisCacheConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
14:09:01.163 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'spring.cache-org.springframework.boot.autoconfigure.cache.CacheProperties' of type [org.springframework.boot.autoconfigure.cache.CacheProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
14:09:01.165 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'org.springframework.boot.autoconfigure.cache.CacheAutoConfiguration' of type [org.springframework.boot.autoconfigure.cache.CacheAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
14:09:01.167 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'cacheManagerCustomizers' of type [org.springframework.boot.autoconfigure.cache.CacheManagerCustomizers] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
14:09:01.177 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'spring.redis-org.springframework.boot.autoconfigure.data.redis.RedisProperties' of type [org.springframework.boot.autoconfigure.data.redis.RedisProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
14:09:01.181 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration' of type [org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
14:09:01.246 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'lettuceClientResources' of type [io.lettuce.core.resource.DefaultClientResources] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
14:09:01.292 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'redisConnectionFactory' of type [org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
14:09:01.309 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'cacheManager' of type [org.springframework.data.redis.cache.RedisCacheManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
14:09:01.322 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'jpaVendorAdapter' of type [org.springframework.orm.jpa.vendor.HibernateJpaVendorAdapter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
14:09:01.326 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'entityManagerFactoryBuilder' of type [org.springframework.boot.orm.jpa.EntityManagerFactoryBuilder] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
14:09:02.721 [main] INFO  c.a.d.p.DruidDataSource - [init,983] - {dataSource-1} inited
14:09:02.943 [main] INFO  o.h.j.i.u.LogHelper - [logPersistenceUnitInformation,31] - HHH000204: Processing PersistenceUnitInfo [name: default]
14:09:02.977 [main] INFO  o.hibernate.Version - [logVersion,44] - HHH000412: Hibernate ORM core version 5.4.32.Final
14:09:03.108 [main] INFO  o.h.a.common.Version - [<clinit>,56] - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
14:09:03.198 [main] INFO  o.h.dialect.Dialect - [<init>,175] - HHH000400: Using dialect: org.hibernate.dialect.MySQL8Dialect
14:09:03.405 [main] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - [initiateService,52] - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
14:09:03.421 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - [buildNativeEntityManagerFactory,437] - Initialized JPA EntityManagerFactory for persistence unit 'default'
14:09:03.452 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'entityManagerFactory' of type [org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
14:09:03.463 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'entityManagerFactory' of type [com.sun.proxy.$Proxy123] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
14:09:03.468 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'transactionManager' of type [org.springframework.orm.jpa.JpaTransactionManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
14:09:03.472 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'txAdviceInterceptor' of type [com.sccl.framework.common.transaction.TxAdviceInterceptor$$EnhancerBySpringCGLIB$$1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
14:09:03.491 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'txAdvice' of type [org.springframework.transaction.interceptor.TransactionInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
14:09:03.491 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'txAdviceAdvisor' of type [org.springframework.aop.support.DefaultPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
14:09:03.925 [main] INFO  o.s.b.w.e.t.TomcatWebServer - [initialize,108] - Tomcat initialized with port(s): 8080 (http)
14:09:03.943 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-8080"]
14:09:03.944 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:09:03.944 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.55]
14:09:04.069 [main] INFO  o.a.c.c.C.[.[.[/energy-cost] - [log,173] - Initializing Spring embedded WebApplicationContext
14:09:04.069 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - [prepareWebApplicationContext,289] - Root WebApplicationContext: initialization completed in 6675 ms
14:09:04.301 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - mybatis plus join properties config complete
14:09:04.343 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - mybatis plus join SqlInjector init
14:09:04.722 [main] INFO  o.d.c.k.b.i.ClasspathKieProject - [notifyKieModuleFound,152] - Found kmodule: file:/E:/cl-project/ln-nenghao/brch-ln/sccl-web/target/classes/META-INF/kmodule.xml
14:09:04.898 [main] INFO  o.d.c.k.b.i.ClasspathKieProject - [generatePomPropertiesFromPom,376] - Recursed up folders, found and used pom.xml E:\cl-project\ln-nenghao\brch-ln\sccl-web\pom.xml
14:09:06.168 [main] INFO  c.s.m.b.s.u.StationAuditUtil - [<clinit>,76] - accountService getBean is null
14:09:06.267 [main] INFO  c.s.t.f.u.i.PropertiesHolder - [load,80] - 加载资源文件：timing-config-protocol.yml
14:09:06.290 [main] INFO  c.s.t.f.u.i.PropertiesHolder - [load,80] - 加载资源文件：timing-config-sta.yml
14:09:08.852 [main] INFO  o.a.s.c.e.EhCacheManager - [getCache,169] - Using existing EHCache named [loginRecordCache]
14:09:17.191 [main] INFO  c.s.t.f.u.i.PropertiesHolder - [load,80] - 加载资源文件：timing-config-sta.yml
14:09:19.192 [main] INFO  o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - [lambda$buildAutowiringMetadata$1,478] - Autowired annotation is not supported on static fields: private static org.springframework.web.client.RestTemplate com.sccl.modules.util.WorkflowUtil.restTemplate
14:09:19.810 [main] INFO  s.d.s.w.PropertySourcedRequestMappingHandlerMapping - [initHandlerMethods,69] - Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2Controller#getDocumentation(String, HttpServletRequest)]
14:09:19.998 [main] INFO  o.s.b.d.a.OptionalLiveReloadServer - [startServer,58] - LiveReload server is running on port 35729
14:09:20.037 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - [<init>,53] - Adding welcome page: class path resource [META-INF/resources/index.html]
14:09:20.837 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-8080"]
14:09:20.857 [main] INFO  o.s.b.w.e.t.TomcatWebServer - [start,220] - Tomcat started on port(s): 8080 (http) with context path '/energy-cost'
14:09:20.858 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - [start,147] - Context refreshed
14:09:20.871 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - [start,150] - Found 5 custom documentation plugin(s)
14:09:21.224 [main] INFO  s.d.s.w.s.ApiListingReferenceScanner - [scan,41] - Scanning for api listing references
14:09:21.689 [main] INFO  s.d.s.w.s.ApiListingReferenceScanner - [scan,41] - Scanning for api listing references
14:09:21.967 [main] INFO  s.d.s.w.s.ApiListingReferenceScanner - [scan,41] - Scanning for api listing references
14:09:22.206 [main] INFO  s.d.s.w.s.ApiListingReferenceScanner - [scan,41] - Scanning for api listing references
14:09:22.234 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingGET_1
14:09:22.266 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingGET_1
14:09:22.287 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addSaveUsingPOST_1
14:09:22.317 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingGET_2
14:09:22.323 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editSaveUsingPOST_1
14:09:22.332 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getUserInfoUsingGET_1
14:09:22.339 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_1
14:09:22.341 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingHEAD_1
14:09:22.342 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingPOST_1
14:09:22.344 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingPUT_1
14:09:22.346 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingPATCH_1
14:09:22.348 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingDELETE_1
14:09:22.350 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingOPTIONS_1
14:09:22.352 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingTRACE_1
14:09:22.355 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_1
14:09:22.358 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: resetPwdUsingGET_1
14:09:22.362 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: resetPwdUsingPOST_1
14:09:22.652 [main] INFO  s.d.s.w.s.ApiListingReferenceScanner - [scan,41] - Scanning for api listing references
14:09:22.668 [main] INFO  com.sccl.Application - [logStarted,61] - Started Application in 25.971 seconds (JVM running for 29.243)
14:09:53.739 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[.[.[/energy-cost] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:09:53.739 [http-nio-8080-exec-1] INFO  o.s.w.s.DispatcherServlet - [initServletBean,525] - Initializing Servlet 'dispatcherServlet'
14:09:53.742 [http-nio-8080-exec-1] INFO  o.s.w.s.DispatcherServlet - [initServletBean,547] - Completed initialization in 3 ms
14:09:56.426 [http-nio-8080-exec-3] INFO  c.s.c.s.j.r.UserRealm - [doGetAuthorizationInfo,90] - 用户：libingran-李冰然权限过期时间：107999789704
14:09:56.426 [http-nio-8080-exec-2] INFO  c.s.c.s.j.r.UserRealm - [doGetAuthorizationInfo,90] - 用户：libingran-李冰然权限过期时间：107999789704
14:09:56.426 [http-nio-8080-exec-3] INFO  c.s.c.s.j.r.UserRealm - [doGetAuthorizationInfo,102] - 调用鉴权方法，获取到的权限条数103
14:09:56.426 [http-nio-8080-exec-2] INFO  c.s.c.s.j.r.UserRealm - [doGetAuthorizationInfo,102] - 调用鉴权方法，获取到的权限条数103
14:10:03.797 [http-nio-8080-exec-6] INFO  c.s.m.s.a.c.AuthController - [ajaxLogin,309] - 用户：libingran-李冰然权限信息共103条已存入Redis，过期时间：********
14:10:03.819 [http-nio-8080-exec-6] INFO  c.s.c.hlog.HLogUtils - [writeLoginLog,121] - {"server":"sysPA","sysCode":"S000AN2021012048","remoteIp":"127.0.0.1","loginTime":"2025-08-04 14:10:03.808","logAppId":"QX661449_scnh_NHF_sys_PA","serialNbr":"202508044287803808","loginStatus":"1","authType":"11","account":"libingran","userCode":"libingran"}
14:10:04.831 [http-nio-8080-exec-9] INFO  c.s.c.s.j.r.UserRealm - [doGetAuthorizationInfo,90] - 用户：libingran-李冰然权限过期时间：********
14:10:04.832 [http-nio-8080-exec-9] INFO  c.s.c.s.j.r.UserRealm - [doGetAuthorizationInfo,102] - 调用鉴权方法，获取到的权限条数103
14:13:24.859 [http-nio-8080-exec-56] INFO  c.s.m.b.a.c.AccountController - [selfAccountList,234] - 第一步耗时 :0.0s
14:13:25.354 [http-nio-8080-exec-56] INFO  c.s.m.b.a.c.AccountController - [selfAccountList,240] - 第二步耗时 :0.495s
14:13:25.355 [http-nio-8080-exec-56] INFO  c.s.m.b.a.s.AccountServiceImpl - [selectSelfList,147] - 台账录入 baseresultList开始执行
14:13:26.115 [http-nio-8080-exec-56] INFO  c.s.m.b.a.s.AccountServiceImpl - [selectSelfList,155] - 台账录入 baseresultList执行结束
14:13:26.487 [http-nio-8080-exec-56] INFO  c.s.m.b.a.c.AccountController - [selfAccountList,269] - 第三步耗时 :1.133s
14:13:26.488 [http-nio-8080-exec-56] INFO  c.s.m.b.a.c.AccountController - [selfAccountList,271] - 设置固定参数
14:13:26.488 [http-nio-8080-exec-56] INFO  c.s.m.b.a.c.AccountController - [selfAccountList,277] - 设置固定参数结束
14:13:26.489 [http-nio-8080-exec-56] INFO  c.s.m.b.a.c.AccountController - [selfAccountList,290] - 第四步耗时 :0.002s
14:13:26.489 [http-nio-8080-exec-56] INFO  c.s.m.b.a.c.AccountController - [selfAccountList,291] - 总耗时 :1.63s
16:13:56.648 [http-nio-8080-exec-78] INFO  c.s.c.s.j.r.UserRealm - [doGetAuthorizationInfo,90] - 用户：libingran-李冰然权限过期时间：********
16:13:56.649 [http-nio-8080-exec-78] INFO  c.s.c.s.j.r.UserRealm - [doGetAuthorizationInfo,102] - 调用鉴权方法，获取到的权限条数103
16:14:01.774 [http-nio-8080-exec-2] INFO  c.s.m.b.a.c.AmmeterorprotocolController - [list,111] - 协议管理查询
16:14:02.547 [http-nio-8080-exec-2] INFO  c.s.m.b.a.c.AmmeterorprotocolController - [list,119] - 协议管理查询结束
16:14:07.923 [http-nio-8080-exec-11] INFO  c.s.m.b.a.c.AmmeterorprotocolController - [list,111] - 协议管理查询
16:14:08.066 [http-nio-8080-exec-11] INFO  c.s.m.b.a.c.AmmeterorprotocolController - [list,119] - 协议管理查询结束
17:12:34.656 [SpringContextShutdownHook] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - [destroy,651] - Closing JPA EntityManagerFactory for persistence unit 'default'
17:12:35.677 [SpringContextShutdownHook] INFO  o.h.j.i.u.LogHelper - [logPersistenceUnitInformation,31] - HHH000204: Processing PersistenceUnitInfo [name: default]
17:12:35.763 [SpringContextShutdownHook] INFO  o.h.dialect.Dialect - [<init>,175] - HHH000400: Using dialect: org.hibernate.dialect.MySQL8Dialect
17:12:35.781 [SpringContextShutdownHook] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - [initiateService,52] - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
17:12:35.920 [SpringContextShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2024] - {dataSource-0} closing ...
