# brch-ln 项目增删改查开发规则

## 项目概述

brch-ln 是一个基于 Spring Boot + Vue 2.x 的能耗费管理系统，采用前后端分离架构。

### 技术栈
- **后端**: Spring Boot + MyBatis-Plus + MySQL
- **前端**: Vue 2.x + iView + Element UI + cl组件库
- **构建工具**: Maven + Vue CLI

## 后端开发规范

### 1. 项目结构

```
brch-ln/
├── sccl-web/                    # Web启动模块
├── sccl-module-business/        # 业务模块
├── sccl-module-base/           # 基础模块
├── sccl-framework/             # 框架核心
├── sccl-common/                # 公共工具
└── carbon-*/                   # 碳排放相关模块
```

### 2. 包结构规范

```
com.sccl.modules.business.{模块名}/
├── controller/                 # 控制器层
├── service/                   # 服务层
│   └── impl/                  # 服务实现
├── mapper/                    # 数据访问层
├── domain/                    # 实体类
├── dto/                       # 数据传输对象
└── vo/                        # 视图对象
```

### 3. 增删改查标准实现

#### 3.1 Controller 层规范

```java
@RestController
@RequestMapping("/business/{模块名}")
public class {模块名}Controller extends BaseController {
    
    @Autowired
    private I{模块名}Service {模块名}Service;
    
    /**
     * 查询列表 - 支持分页
     */
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(@RequestBody {模块名}QueryDTO queryDTO) {
        startPage(); // 开启分页
        List<{模块名}VO> list = {模块名}Service.selectList(queryDTO);
        return getDataTable(list);
    }
    
    /**
     * 查询详情
     */
    @GetMapping("/view/{id}")
    @ResponseBody
    public AjaxResult view(@PathVariable("id") Long id) {
        {模块名}VO vo = {模块名}Service.getDetailById(id);
        return success(vo);
    }
    
    /**
     * 新增
     */
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult add(@RequestBody {模块名}AddDTO addDTO) {
        return toAjax({模块名}Service.insert(addDTO));
    }
    
    /**
     * 修改
     */
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult edit(@RequestBody {模块名}UpdateDTO updateDTO) {
        return toAjax({模块名}Service.update(updateDTO));
    }
    
    /**
     * 删除 - 支持批量删除
     */
    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(@RequestBody Map<String, String> params) {
        String ids = params.get("ids");
        return toAjax({模块名}Service.deleteByIds(Convert.toStrArray(ids)));
    }
}
```

#### 3.2 Service 层规范

```java
// 接口定义
public interface I{模块名}Service extends IBaseService<{模块名}> {
    List<{模块名}VO> selectList({模块名}QueryDTO queryDTO);
    {模块名}VO getDetailById(Long id);
    boolean insert({模块名}AddDTO addDTO);
    boolean update({模块名}UpdateDTO updateDTO);
    boolean deleteByIds(String[] ids);
}

// 实现类
@Service
public class {模块名}ServiceImpl extends BaseServiceImpl<{模块名}> implements I{模块名}Service {
    
    @Autowired
    private {模块名}Mapper {模块名}Mapper;
    
    @Override
    public List<{模块名}VO> selectList({模块名}QueryDTO queryDTO) {
        return {模块名}Mapper.selectList(queryDTO);
    }
    
    @Override
    public {模块名}VO getDetailById(Long id) {
        {模块名} entity = this.getById(id);
        if (ObjUtil.isEmpty(entity)) {
            throw new ServiceException("数据不存在");
        }
        {模块名}VO vo = new {模块名}VO();
        BeanUtils.copyProperties(entity, vo);
        return vo;
    }
    
    @Override
    public boolean insert({模块名}AddDTO addDTO) {
        {模块名} entity = new {模块名}();
        BeanUtils.copyProperties(addDTO, entity);
        // 设置创建信息
        entity.setCreateTime(new Date());
        entity.setCreateBy(SecurityUtils.getLoginUser().getUserId());
        return this.save(entity);
    }
    
    @Override
    public boolean update({模块名}UpdateDTO updateDTO) {
        {模块名} entity = this.getById(updateDTO.getId());
        if (ObjUtil.isEmpty(entity)) {
            throw new ServiceException("数据不存在");
        }
        BeanUtils.copyProperties(updateDTO, entity);
        // 设置更新信息
        entity.setUpdateTime(new Date());
        entity.setUpdateBy(SecurityUtils.getLoginUser().getUserId());
        return this.updateById(entity);
    }
    
    @Override
    public boolean deleteByIds(String[] ids) {
        // 逻辑删除
        List<Long> idList = Arrays.stream(ids)
            .map(Long::valueOf)
            .collect(Collectors.toList());
        return this.removeByIds(idList);
    }
}
```

#### 3.3 Mapper 层规范

```java
// Mapper接口
@Mapper
public interface {模块名}Mapper extends BaseMapper<{模块名}> {
    List<{模块名}VO> selectList({模块名}QueryDTO queryDTO);
}
```

```xml
<!-- Mapper XML -->
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sccl.modules.business.{模块名}.mapper.{模块名}Mapper">
    
    <select id="selectList" parameterType="com.sccl.modules.business.{模块名}.dto.{模块名}QueryDTO" 
            resultType="com.sccl.modules.business.{模块名}.vo.{模块名}VO">
        SELECT 
            id,
            name,
            code,
            status,
            create_time,
            create_by
        FROM {表名}
        WHERE del_flag = '0'
        <if test="name != null and name != ''">
            AND name LIKE CONCAT('%', #{name}, '%')
        </if>
        <if test="status != null and status != ''">
            AND status = #{status}
        </if>
        ORDER BY create_time DESC
    </select>
    
</mapper>
```

#### 3.4 DTO/VO 规范

```java
// 查询DTO
public class {模块名}QueryDTO implements Serializable {
    private String name;
    private String status;
    private Date startTime;
    private Date endTime;
    // getter/setter...
}

// 新增DTO
public class {模块名}AddDTO implements Serializable {
    @NotBlank(message = "名称不能为空")
    private String name;
    
    @NotBlank(message = "编码不能为空")
    private String code;
    
    private String status;
    private String remark;
    // getter/setter...
}

// 修改DTO
public class {模块名}UpdateDTO implements Serializable {
    @NotNull(message = "ID不能为空")
    private Long id;
    
    @NotBlank(message = "名称不能为空")
    private String name;
    
    private String status;
    private String remark;
    // getter/setter...
}

// 返回VO
public class {模块名}VO implements Serializable {
    private String id; // 前端需要字符串类型避免精度丢失
    private String name;
    private String code;
    private String status;
    private String statusName;
    private Date createTime;
    private String createBy;
    // getter/setter...
}
```

### 4. 开发规范要点

#### 4.1 Controller 规范
- 继承 `BaseController`
- 不写 try-catch，使用全局异常处理
- 使用 `@RequestBody` 接收JSON参数
- 返回统一的 `AjaxResult` 或 `TableDataInfo`
- 查询列表使用 `startPage()` 开启分页

#### 4.2 Service 规范
- 接口继承 `IBaseService<T>`
- 实现类继承 `BaseServiceImpl<T>`
- 优先使用 MyBatis-Plus 的方法
- 复杂查询写 Mapper XML
- 使用 `BeanUtils.copyProperties()` 进行对象拷贝
- 关键判断处添加注释

#### 4.3 数据校验规范
- 使用 JSR-303 注解进行参数校验
- 非空判断使用 Hutool 工具类
- 业务校验在 Service 层进行

#### 4.4 异常处理规范
- 使用 `ServiceException` 抛出业务异常
- 不在 Controller 层处理异常
- 统一异常信息格式

## 前端开发规范

### 1. 目录结构

```
src/
├── api/                       # API接口定义
│   └── basedata/             # 按模块组织
├── view/                     # 页面组件
│   └── basedata/             # 按模块组织
└── components/cl/            # 自定义组件库
```

### 2. 页面开发规范

#### 2.1 标准页面模板

```vue
<template>
  <card class="menu-card" dis-hover style="padding: 0;margin: 0">
    <Spin size="large" fix v-if="loading"></Spin>
    
    <!-- 弹窗组件 -->
    <{模块名}-modal ref="{模块名}Modal" @on-success="handleModalSuccess"></{模块名}-modal>
    
    <!-- 主要内容 -->
    <cl-table
      ref="table"
      :data="tableData"
      :columns="columns"
      :loading="loading"
      :total="total"
      :searchable="true"
      select-enabled
      select-multiple
      @on-search="handleSearch"
      @on-reset="handleReset"
      @on-selection-change="handleSelectRow"
      @on-query="handleQuery"
    >
      <!-- 搜索表单 -->
      <template slot="filter">
        <Row>
          <Col span="6">
            <FormItem label="名称:">
              <cl-input v-model="queryParams.name" :clearable="true"></cl-input>
            </FormItem>
          </Col>
          <Col span="6">
            <FormItem label="状态:">
              <cl-select v-model="queryParams.status" category="STATUS_TYPE"></cl-select>
            </FormItem>
          </Col>
        </Row>
      </template>
      
      <!-- 操作按钮 -->
      <template slot="buttons">
        <Button type="primary" @click="handleAdd">新增</Button>
        <Button type="warning" @click="handleEdit">编辑</Button>
        <Button type="error" @click="handleDelete">删除</Button>
      </template>
    </cl-table>
  </card>
</template>

<script>
import { list{模块名}, delete{模块名} } from '@/api/basedata/{模块名}'

export default {
  name: '{模块名}List',
  data() {
    return {
      loading: false,
      tableData: [],
      total: 0,
      selectedRows: [],
      queryParams: {
        name: '',
        status: ''
      },
      columns: [
        { title: 'ID', key: 'id', width: 80 },
        { title: '名称', key: 'name' },
        { title: '编码', key: 'code' },
        { title: '状态', key: 'statusName' },
        { title: '创建时间', key: 'createTime', width: 160 }
      ]
    }
  },
  mounted() {
    this.getList()
  },
  methods: {
    async getList() {
      this.loading = true
      try {
        const res = await list{模块名}(this.queryParams)
        this.tableData = res.data.rows
        this.total = res.data.total
      } catch (error) {
        this.$Message.error('获取数据失败')
      } finally {
        this.loading = false
      }
    },
    
    handleSearch(params) {
      this.queryParams = { ...this.queryParams, ...params }
      this.getList()
    },
    
    handleReset() {
      this.queryParams = {
        name: '',
        status: ''
      }
      this.getList()
    },
    
    handleQuery(pagination) {
      this.queryParams.pageNum = pagination.pageNum
      this.queryParams.pageSize = pagination.pageSize
      this.getList()
    },
    
    handleSelectRow(selection) {
      this.selectedRows = selection
    },
    
    handleAdd() {
      this.$refs.{模块名}Modal.show('add')
    },
    
    handleEdit() {
      if (this.selectedRows.length !== 1) {
        this.$Message.warning('请选择一条数据进行编辑')
        return
      }
      this.$refs.{模块名}Modal.show('edit', this.selectedRows[0])
    },
    
    async handleDelete() {
      if (this.selectedRows.length === 0) {
        this.$Message.warning('请选择要删除的数据')
        return
      }
      
      this.$Modal.confirm({
        title: '确认删除',
        content: `确定要删除选中的 ${this.selectedRows.length} 条数据吗？`,
        onOk: async () => {
          try {
            const ids = this.selectedRows.map(row => row.id).join(',')
            await delete{模块名}({ ids })
            this.$Message.success('删除成功')
            this.getList()
          } catch (error) {
            this.$Message.error('删除失败')
          }
        }
      })
    },
    
    handleModalSuccess() {
      this.getList()
    }
  }
}
</script>
```

#### 2.2 API 接口规范

```javascript
// src/api/basedata/{模块名}.js
import axios from '@/libs/api.request'

// 查询列表
export const list{模块名} = (params) => {
  return axios.request({
    url: 'business/{模块名}/list',
    data: params,
    method: 'post'
  })
}

// 查询详情
export const view{模块名} = (id) => {
  return axios.request({
    url: `business/{模块名}/view/${id}`,
    method: 'get'
  })
}

// 新增
export const add{模块名} = (data) => {
  return axios.request({
    url: 'business/{模块名}/add',
    data: data,
    method: 'post'
  })
}

// 修改
export const update{模块名} = (data) => {
  return axios.request({
    url: 'business/{模块名}/edit',
    data: data,
    method: 'post'
  })
}

// 删除
export const delete{模块名} = (params) => {
  return axios.request({
    url: 'business/{模块名}/remove',
    data: params,
    method: 'post'
  })
}
```

### 3. 组件使用规范

#### 3.1 优先级顺序
1. **cl组件** (最高优先级) - 项目自定义组件
2. **iView组件** - 主要UI框架  
3. **Element UI组件** - 辅助UI组件

#### 3.2 必须使用的组件
- **表格**: 强制使用 `cl-table`，不使用原生表格
- **输入框**: 使用 `cl-input`，支持清除功能
- **下拉选择**: 使用 `cl-select`，支持字典数据

### 4. 命名规范

#### 4.1 文件命名
- **页面文件**: camelCase，如 `stationList.vue`
- **组件文件**: PascalCase，如 `StationModal.vue`

#### 4.2 变量命名
- **方法名**: camelCase，动词开头，如 `handleAdd`、`getList`
- **变量名**: camelCase，如 `tableData`、`queryParams`
- **组件名**: PascalCase，如 `StationList`

## 开发流程

### 1. 后端开发流程
1. 创建数据库表
2. 生成实体类
3. 创建 DTO/VO 类
4. 编写 Mapper 接口和 XML
5. 编写 Service 接口和实现
6. 编写 Controller
7. 测试接口

### 2. 前端开发流程
1. 创建 API 接口文件
2. 创建页面组件
3. 配置路由
4. 测试功能

### 3. 联调测试
1. 接口联调
2. 功能测试
3. 异常处理测试

## 注意事项

### 1. 数据安全
- 前端传递 ID 使用字符串类型避免精度丢失
- 敏感操作需要权限校验
- 删除操作使用逻辑删除

### 2. 性能优化
- 查询列表必须支持分页
- 大数据量操作考虑异步处理
- 合理使用索引

### 3. 用户体验
- 操作反馈及时
- 错误信息友好
- 加载状态明确

### 4. 代码质量
- 遵循命名规范
- 添加必要注释
- 保持代码简洁

## 常用工具类和方法

### 1. 后端工具类

#### 1.1 对象拷贝
```java
// 优先使用 Spring 框架的 BeanUtils
import org.springframework.beans.BeanUtils;
BeanUtils.copyProperties(source, target);

// 属性不一致时，先拷贝再单独赋值
BeanUtils.copyProperties(dto, entity);
entity.setSpecialField(dto.getSpecialValue());
```

#### 1.2 非空判断 (Hutool)
```java
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.collection.CollUtil;

// 字符串判断
StrUtil.isNotBlank(str)
StrUtil.isBlank(str)

// 对象判断
ObjUtil.isEmpty(obj)
ObjUtil.isNotEmpty(obj)

// 集合判断
CollUtil.isEmpty(collection)
CollUtil.isNotEmpty(collection)
```

#### 1.3 分页查询
```java
// Controller 中开启分页
startPage();
List<Entity> list = service.selectList(params);
return getDataTable(list);
```

### 2. 前端工具方法

#### 2.1 API 请求统一处理
```javascript
// 统一使用 @/libs/api.request
import axios from '@/libs/api.request'

// POST 请求传 data
export const addData = (data) => {
  return axios.request({
    url: 'business/module/add',
    data: data,
    method: 'post'
  })
}

// GET 请求传 params
export const getData = (params) => {
  return axios.request({
    url: 'business/module/get',
    params: params,
    method: 'get'
  })
}
```

#### 2.2 表格列定义
```javascript
columns: [
  { title: 'ID', key: 'id', width: 80 },
  { title: '名称', key: 'name', minWidth: 120 },
  { title: '状态', key: 'statusName', width: 100 },
  {
    title: '操作',
    key: 'action',
    width: 150,
    render: (h, params) => {
      return h('div', [
        h('Button', {
          props: { type: 'primary', size: 'small' },
          on: { click: () => this.handleView(params.row) }
        }, '查看'),
        h('Button', {
          props: { type: 'warning', size: 'small' },
          style: { marginLeft: '5px' },
          on: { click: () => this.handleEdit(params.row) }
        }, '编辑')
      ])
    }
  }
]
```

## 错误处理规范

### 1. 后端异常处理
```java
// Service 层抛出业务异常
if (ObjUtil.isEmpty(entity)) {
    throw new ServiceException("数据不存在");
}

if (StrUtil.isBlank(dto.getName())) {
    throw new ServiceException("名称不能为空");
}

// 数据库操作异常
try {
    this.save(entity);
} catch (Exception e) {
    log.error("保存数据失败", e);
    throw new ServiceException("保存失败");
}
```

### 2. 前端异常处理
```javascript
// 统一异常处理
async handleSave() {
  try {
    this.loading = true
    await addData(this.formData)
    this.$Message.success('保存成功')
    this.handleCancel()
    this.$emit('on-success')
  } catch (error) {
    this.$Message.error(error.message || '保存失败')
  } finally {
    this.loading = false
  }
}
```

## 数据字典使用

### 1. 后端枚举定义
```java
public enum StatusEnum {
    ENABLE("0", "启用"),
    DISABLE("1", "禁用");

    private final String code;
    private final String name;

    StatusEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    // getter methods...
}
```

### 2. 前端字典使用
```vue
<!-- 使用 cl-select 自动加载字典 -->
<cl-select
  v-model="formData.status"
  category="STATUS_TYPE"
  labelField="typeName"
  valueField="typeCode">
</cl-select>

<!-- 手动定义选项 -->
<Select v-model="formData.status">
  <Option value="0">启用</Option>
  <Option value="1">禁用</Option>
</Select>
```

## 权限控制

### 1. 后端权限注解
```java
@RequiresPermissions("business:module:list")
@PostMapping("/list")
public TableDataInfo list(@RequestBody QueryDTO queryDTO) {
    // 查询逻辑
}

@RequiresPermissions("business:module:add")
@PostMapping("/add")
public AjaxResult add(@RequestBody AddDTO addDTO) {
    // 新增逻辑
}
```

### 2. 前端权限控制
```vue
<!-- 按钮权限控制 -->
<Button
  v-if="hasPermission('business:module:add')"
  type="primary"
  @click="handleAdd">
  新增
</Button>

<!-- 路由权限配置 -->
{
  path: 'module',
  name: 'module',
  meta: {
    title: '模块管理',
    access: ['/business/module']
  },
  component: () => import('@/view/business/module/index.vue')
}
```

## 日志记录

### 1. 后端日志
```java
// 使用 @Log 注解记录操作日志
@Log(title = "模块管理", action = BusinessType.INSERT)
@PostMapping("/add")
public AjaxResult add(@RequestBody AddDTO addDTO) {
    // 新增逻辑
}

// 手动记录日志
@Slf4j
@Service
public class ModuleServiceImpl {

    public boolean save(AddDTO addDTO) {
        log.info("开始保存模块数据: {}", addDTO.getName());
        try {
            // 保存逻辑
            log.info("模块数据保存成功: {}", addDTO.getName());
            return true;
        } catch (Exception e) {
            log.error("模块数据保存失败: {}", addDTO.getName(), e);
            throw new ServiceException("保存失败");
        }
    }
}
```

## 数据验证

### 1. 后端参数校验
```java
// DTO 中使用 JSR-303 注解
public class AddDTO {
    @NotBlank(message = "名称不能为空")
    @Length(max = 50, message = "名称长度不能超过50个字符")
    private String name;

    @NotBlank(message = "编码不能为空")
    @Pattern(regexp = "^[A-Z0-9_]+$", message = "编码只能包含大写字母、数字和下划线")
    private String code;

    @NotNull(message = "状态不能为空")
    private String status;
}

// Controller 中启用校验
@PostMapping("/add")
public AjaxResult add(@Valid @RequestBody AddDTO addDTO) {
    return toAjax(service.insert(addDTO));
}
```

### 2. 前端表单校验
```vue
<template>
  <Form ref="form" :model="formData" :rules="rules">
    <FormItem label="名称" prop="name">
      <cl-input v-model="formData.name"></cl-input>
    </FormItem>
    <FormItem label="编码" prop="code">
      <cl-input v-model="formData.code"></cl-input>
    </FormItem>
  </Form>
</template>

<script>
export default {
  data() {
    return {
      formData: {
        name: '',
        code: ''
      },
      rules: {
        name: [
          { required: true, message: '名称不能为空', trigger: 'blur' },
          { max: 50, message: '名称长度不能超过50个字符', trigger: 'blur' }
        ],
        code: [
          { required: true, message: '编码不能为空', trigger: 'blur' },
          { pattern: /^[A-Z0-9_]+$/, message: '编码只能包含大写字母、数字和下划线', trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    handleSave() {
      this.$refs.form.validate(async (valid) => {
        if (valid) {
          // 提交数据
        }
      })
    }
  }
}
</script>
```

## 性能优化建议

### 1. 后端优化
- 查询列表必须支持分页，避免一次性加载大量数据
- 复杂查询使用索引优化
- 批量操作使用事务处理
- 大数据量导出使用异步处理

### 2. 前端优化
- 表格数据使用虚拟滚动（数据量大时）
- 图片懒加载
- 路由懒加载
- 合理使用 v-if 和 v-show

## 测试规范

### 1. 接口测试
- 使用 Postman 或类似工具测试所有接口
- 测试正常流程和异常流程
- 验证参数校验是否生效
- 测试权限控制是否正确

### 2. 功能测试
- 测试增删改查的完整流程
- 测试分页、搜索、排序功能
- 测试表单校验
- 测试异常情况的处理

## 部署注意事项

### 1. 环境配置
- 开发环境: `dev-ln` (辽宁) / `dev-sc` (四川)
- 生产环境: `build-ln` / `build-sc`

### 2. 数据库脚本
- 新增表结构要提供 SQL 脚本
- 数据字典要同步更新
- 权限配置要提供 SQL 脚本

### 3. 配置文件
- 检查配置文件中的环境变量
- 确认数据库连接配置
- 验证文件上传路径配置

这个增删改查开发规则文档涵盖了 brch-ln 项目的完整开发流程和规范，开发人员可以按照这个规则进行标准化开发，确保代码质量和项目的一致性。
