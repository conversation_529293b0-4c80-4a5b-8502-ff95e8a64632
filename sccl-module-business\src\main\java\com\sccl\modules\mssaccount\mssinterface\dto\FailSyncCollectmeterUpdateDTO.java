package com.sccl.modules.mssaccount.mssinterface.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 智能电表同步日志修改DTO
 *
 * <AUTHOR>
 * @date 2025-01-25
 */
@Data
public class FailSyncCollectmeterUpdateDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 主键ID */
    @NotNull(message = "ID不能为空")
    private Long id;

    /** 采集时间 */
    private String collectTime;

    /** 省份编码 */
    private String provinceCode;

    /** 地市编码 */
    private String cityCode;

    /** 地市名称 */
    private String cityName;

    /** 区县编码 */
    private String countyCode;

    /** 区县名称 */
    private String countyName;

    /** 站址编码 */
    private String stationCode;

    /** 站址名称 */
    private String stationName;

    /** 交流电量数据 */
    private String acData;

    /** 交流电量数据来源 */
    private String acDataSource;

    /** 油机发电量数据 */
    private String oepgData;

    /** 油机发电量数据来源 */
    private String oepgDataSource;

    /** 光伏发电量数据 */
    private String pvpgData;

    /** 光伏发电量数据来源 */
    private String pvpgDataSource;

    /** 父站址编码 */
    private String parentStationCode;

    /** 载波器 */
    private String ccoer;

    /** 载波器数据采集失败 */
    private String cdcf;

    /** 能耗数据 */
    private String energyData;

    /** 能耗数据来源 */
    private String energyDataSource;

    /** 设备数据 */
    private String deviceData;

    /** 设备数据来源 */
    private String deviceDataSource;

    /** 生产数据 */
    private String productionData;

    /** 生产数据来源 */
    private String productionDataSource;

    /** 管理数据 */
    private String managementData;

    /** 管理数据来源 */
    private String managementDataSource;

    /** 业务数据 */
    private String businessData;

    /** 业务数据来源 */
    private String businessDataSource;

    /** 其他数据 */
    private String otherData;

    /** 其他数据来源 */
    private String otherDataSource;

    /** 同步标志 0未同步，1同步成功，2同步失败 */
    private Integer syncFlag;

    /** 失败原因 */
    private String failMag;

    /** 重试次数 */
    private Integer retry;

    /** 同步时间记录 */
    private String syncTime;

    /** 账期 */
    private String budget;
}
