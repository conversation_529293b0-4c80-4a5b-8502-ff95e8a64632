package com.sccl.modules.mssaccount.mssinterface.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 智能电表同步日志统计分析VO
 *
 * <AUTHOR>
 * @date 2025-01-25
 */
@Data
public class FailSyncCollectmeterStatisticsVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 统计月份 */
    private String statisticMonth;

    /** 地市编码 */
    private String cityCode;

    /** 地市名称 */
    private String cityName;

    /** 区县编码 */
    private String countyCode;

    /** 区县名称 */
    private String countyName;

    /** 站址编码 */
    private String stationCode;

    /** 站址名称 */
    private String stationName;

    /** 交流电量数据合计 */
    private BigDecimal totalAcData;

    /** 记录总数 */
    private Integer totalCount;

    /** 同步成功数 */
    private Integer successCount;

    /** 同步失败数 */
    private Integer failCount;

    /** 未同步数 */
    private Integer pendingCount;

    /** 成功率 */
    private BigDecimal successRate;
}
